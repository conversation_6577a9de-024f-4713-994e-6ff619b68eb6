import { defineStore } from 'pinia';
import { ref, computed, reactive, readonly } from 'vue';
import type { Delivery } from '@/types';
import { showToast, formatDate } from '@/utils';
import { DeliveryApi } from '@/utils/api';
import { useAuthStore } from './auth';
import { useSettingsStore } from './settings';
import { getDateRange } from '@/utils/date';

// ===== 优化的数据存储层 =====

// 派单核心数据接口
interface DeliveryCore {
	id: string;
	name: string;
	date: string;
	queueId: string;
	photoRequirements: string;
	isGrouped: boolean;
	mergedCount: number;
	lastUpdate: number;
}

// 派单统计信息接口
interface DeliveryStats {
	total: number;
	completed: number;
	incomplete: number;
	grouped: number;
	byDate: Map<string, { total: number; completed: number }>;
}

// 非响应式数据存储类
class DeliveryDataStorage {
	private deliveries: Map<string, Delivery> = new Map();
	private deliveriesByDate: Map<string, Delivery[]> = new Map();
	private deliveriesByQueue: Map<string, Delivery[]> = new Map();

	// 设置派单数据
	setDeliveries(deliveries: Delivery[]): void {
		console.info('🚀 [DELIVERY STORAGE] 开始存储派单数据');
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		// 清空现有数据
		this.deliveries.clear();
		this.deliveriesByDate.clear();
		this.deliveriesByQueue.clear();

		// 存储派单数据并建立索引
		deliveries.forEach(delivery => {
			this.deliveries.set(delivery.deliveryId, delivery);

			// 按日期索引
			const dateStr = delivery.deliveryDate ? new Date(delivery.deliveryDate).toISOString().split('T')[0] : 'unknown';
			if (!this.deliveriesByDate.has(dateStr)) {
				this.deliveriesByDate.set(dateStr, []);
			}
			this.deliveriesByDate.get(dateStr)!.push(delivery);

			// 按队列ID索引
			const queueId = delivery.queueId || 'unknown';
			if (!this.deliveriesByQueue.has(queueId)) {
				this.deliveriesByQueue.set(queueId, []);
			}
			this.deliveriesByQueue.get(queueId)!.push(delivery);
		});

		const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
		console.info(`🚀 [DELIVERY STORAGE] 数据存储完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
		console.info(`📊 [DELIVERY STORAGE] 存储了 ${deliveries.length} 个派单，按日期分组: ${this.deliveriesByDate.size}，按队列分组: ${this.deliveriesByQueue.size}`);
	}

	// 获取单个派单
	getDelivery(deliveryId: string): Delivery | undefined {
		return this.deliveries.get(deliveryId);
	}

	// 获取所有派单
	getAllDeliveries(): Delivery[] {
		return Array.from(this.deliveries.values());
	}

	// 按日期获取派单
	getDeliveriesByDate(date: string): Delivery[] {
		return this.deliveriesByDate.get(date) || [];
	}

	// 按队列ID获取派单
	getDeliveriesByQueue(queueId: string): Delivery[] {
		return this.deliveriesByQueue.get(queueId) || [];
	}

	// 获取所有日期
	getAllDates(): string[] {
		return Array.from(this.deliveriesByDate.keys()).sort();
	}

	// 获取所有队列ID
	getAllQueueIds(): string[] {
		return Array.from(this.deliveriesByQueue.keys()).sort();
	}

	// 清空数据
	clear(): void {
		this.deliveries.clear();
		this.deliveriesByDate.clear();
		this.deliveriesByQueue.clear();
	}
}

// 创建全局数据存储实例
const deliveryDataStorage = new DeliveryDataStorage();

export const useDeliveryStore = defineStore('delivery', () => {
	// ===== 优化层数据 =====
	// 派单核心数据（轻量响应式）
	const deliveryCores = ref<DeliveryCore[]>([]);

	// 统计信息（响应式）
	const stats = reactive<DeliveryStats>({
		total: 0,
		completed: 0,
		incomplete: 0,
		grouped: 0,
		byDate: new Map()
	});

	// ===== 兼容层数据（保留现有接口） =====
	// 派单列表
	const deliveries = ref<Delivery[]>([]);
	// 加载状态
	const loading = ref(false);
	// 刷新状态
	const refreshing = ref(false);
	// 是否已有数据
	const hasData = ref(false);
	// 本地缓存key
	const CACHE_KEY = 'delivery_cache';
	const SELECTED_IDS_KEY = 'delivery_selected_ids';
	const MANUAL_MERGED_KEY = 'delivery_manual_merged';
	const FILTER_SETTINGS_KEY = 'delivery_filter_settings';

	// 缓存优化相关
	let saveTimer: any = null;
	const SAVE_DEBOUNCE_DELAY = 1000; // 1秒防抖延迟
	let isDirty = false; // 标记数据是否需要保存

	// 选中的派单ID列表
	const selectedDeliveryIds = ref<string[]>([]);
	// 手动合并后的派单
	const manualMergedDelivery = ref<any>(null);
	// 筛选设置
	const filterSettings = ref({
		mergeType: 'no_merge', // 合并类型：no_merge, merge_today, manual_merge
		sortOrder: 'time_asc', // 排序方式：time_asc, time_desc
	});

	// ===== 优化层数据更新函数 =====
	const updateOptimizedData = (deliveryList: Delivery[], taskStore?: any) => {
		console.info('🚀 [DELIVERY OPTIMIZATION] 开始更新优化层数据');
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		// 1. 存储原始数据到非响应式存储
		deliveryDataStorage.setDeliveries(deliveryList);

		// 2. 提取核心业务数据（轻量响应式）
		deliveryCores.value = deliveryList.map(delivery => ({
			id: delivery.deliveryId,
			name: getDeliveryName(delivery),
			date: delivery.deliveryDate ? new Date(delivery.deliveryDate).toISOString().split('T')[0] : 'unknown',
			queueId: delivery.queueId || 'unknown',
			photoRequirements: delivery.photoRequirements || '',
			isGrouped: false,
			mergedCount: 1,
			lastUpdate: Date.now()
		}));

		// 3. 更新统计信息
		stats.total = deliveryList.length;
		stats.completed = 0;
		stats.incomplete = 0;
		stats.grouped = 0;
		stats.byDate.clear();

		// 如果有任务 store，计算完成状态
		if (taskStore) {
			deliveryList.forEach(delivery => {
				const deliveryStats = taskStore.getDeliveryState(delivery.deliveryId);
				const isComplete = deliveryStats.requiredCount > 0 &&
					(deliveryStats.uploadedCount + deliveryStats.pendingCount) >= deliveryStats.requiredCount;

				if (isComplete) {
					stats.completed++;
				} else {
					stats.incomplete++;
				}

				// 按日期统计
				const dateStr = delivery.deliveryDate ? new Date(delivery.deliveryDate).toISOString().split('T')[0] : 'unknown';
				if (!stats.byDate.has(dateStr)) {
					stats.byDate.set(dateStr, { total: 0, completed: 0 });
				}
				const dateStats = stats.byDate.get(dateStr)!;
				dateStats.total++;
				if (isComplete) {
					dateStats.completed++;
				}
			});
		} else {
			stats.incomplete = deliveryList.length;
		}

		const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
		console.info(`🚀 [DELIVERY OPTIMIZATION] 优化层数据更新完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
		console.info(`📊 [DELIVERY STATS] 总派单: ${stats.total}, 已完成: ${stats.completed}, 未完成: ${stats.incomplete}`);
	};

	// 从缓存加载数据
	const loadFromCache = () => {
		try {
			const cached = uni.getStorageSync(CACHE_KEY);
			if (cached) {
				deliveries.value = JSON.parse(cached);
				hasData.value = true;
				return true;
			}
		} catch (error) {
			console.warn('读取缓存失败:', error);
		}
		return false;
	};

	// 立即保存数据到缓存（同步）
	const saveToCacheSync = () => {
		try {
			const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
			uni.setStorageSync(CACHE_KEY, JSON.stringify(deliveries.value));
			const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
			console.info(`💾 [DELIVERY CACHE] 同步保存缓存完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
			isDirty = false;
		} catch (error) {
			console.warn('保存缓存失败:', error);
		}
	};

	// 防抖保存数据到缓存（异步）
	const saveToCache = () => {
		isDirty = true;

		// 清除之前的定时器
		if (saveTimer) {
			clearTimeout(saveTimer);
		}

		// 设置新的定时器
		saveTimer = setTimeout(() => {
			if (isDirty) {
				saveToCacheSync();
			}
		}, SAVE_DEBOUNCE_DELAY);

		console.info(`💾 [DELIVERY CACHE] 已标记需要保存，将在 ${SAVE_DEBOUNCE_DELAY}ms 后执行`);
	};

	// 强制立即保存缓存
	const flushCache = () => {
		if (saveTimer) {
			clearTimeout(saveTimer);
			saveTimer = null;
		}
		if (isDirty) {
			saveToCacheSync();
		}
	};

	// 清除缓存数据（保留用户选择状态）
	const clearCache = () => {
		try {
			// 清除定时器
			if (saveTimer) {
				clearTimeout(saveTimer);
				saveTimer = null;
			}
			isDirty = false;

			// ✅ 清除派单数据缓存（服务端数据，可重新加载）
			uni.removeStorageSync(CACHE_KEY);

			// ❌ 保留用户选择的派单ID（用户工作状态）
			// uni.removeStorageSync(SELECTED_IDS_KEY);

			// ❌ 保留手动合并的派单（用户工作状态）
			// uni.removeStorageSync(MANUAL_MERGED_KEY);

			// ❌ 保留筛选设置（用户偏好设置）
			// uni.removeStorageSync(FILTER_SETTINGS_KEY);

			// ✅ 清除派单数据（服务端数据，可重新加载）
			deliveries.value = [];

			// ❌ 保留用户选择的派单ID（用户工作状态）
			// selectedDeliveryIds.value = [];

			// ❌ 保留手动合并的派单（用户工作状态）
			// manualMergedDelivery.value = null;

			// ❌ 保留筛选设置（用户偏好设置）
			// filterSettings.value = { ... };

			// ✅ 重置加载状态
			hasData.value = false;

			console.info('🗑️ [DELIVERY CACHE] 已清除派单数据缓存（保留用户状态）');
		} catch (error) {
			console.warn('清除缓存失败:', error);
		}
	};

	// 加载派单任务（内部API调用函数）
	const loadDeliveries = async (forceLoad = false) => {
		// 如果已有数据且不是强制加载，尝试从缓存加载
		if (hasData.value && !forceLoad) {
			if (loadFromCache()) return;
		}

		if (!refreshing.value) {
			loading.value = true;
		}

		try {
			const authStore = useAuthStore();
			const settingsStore = useSettingsStore();
			const username = authStore.getUsername();

			// 验证用户名是否有效
			if (!username || username.trim() === '') {
				console.error('❌ [DELIVERY] 用户名为空，无法加载派单');
				showToast('用户信息无效，请重新登录', 'none');
				return;
			}

			// 根据设置获取时间范围
			const { startDay, endDay } = getDateRange(settingsStore.settings.orderPeriod);

			console.info(`📋 [DELIVERY] 从API加载派单: 用户=${username}, 时间范围=${startDay} ~ ${endDay}`);

			const response = await DeliveryApi.loadUserDeliveries(
				username,
				startDay,
				endDay
			);

			deliveries.value = response?.data || [];

			// ===== 性能优化：同时更新优化层数据 =====
			updateOptimizedData(deliveries.value);

			hasData.value = true;
			saveToCacheSync(); // 加载完成后立即保存
			console.info('📋加载了「' + deliveries.value.length + '」条派单');
		} catch (error) {
			console.error('❌ [DELIVERY] 加载派单失败:', error);
			throw error; // 重新抛出错误，让智能数据管理系统处理
		} finally {
			loading.value = false;
		}
	};

	// 刷新派单任务
	const refreshDeliveries = async () => {
		try {
			refreshing.value = true;
			await loadDeliveries(true);
			// 不需要在这里调用 afterLoadDeliveries，因为 loadDeliveries 已经被包装
		} finally {
			refreshing.value = false;
		}
	};

	// 获取派单对应的派单名称
	const getDeliveryName = (delivery: Delivery) => {
		const nameParts = [
			delivery?.deliveryName || '',
			delivery?.photoRequirements ? `(${delivery.photoRequirements})` : '',
		].filter((part) => part && part.trim());
		return nameParts.join(' ') || '未设置';
	};

	// 从本地存储加载状态
	const loadStateFromStorage = () => {
		try {
			// 加载选中的派单ID列表
			const savedSelectedIds = uni.getStorageSync(SELECTED_IDS_KEY);
			if (savedSelectedIds) {
				selectedDeliveryIds.value = JSON.parse(savedSelectedIds);
			}

			// 加载手动合并后的派单
			const savedManualMerged = uni.getStorageSync(MANUAL_MERGED_KEY);
			if (savedManualMerged) {
				manualMergedDelivery.value = JSON.parse(savedManualMerged);
			}

			// 加载筛选设置
			const savedFilterSettings = uni.getStorageSync(FILTER_SETTINGS_KEY);
			if (savedFilterSettings) {
				filterSettings.value = JSON.parse(savedFilterSettings);
			}
		} catch (error) {
			console.warn('加载状态失败:', error);
		}
	};

	// 保存状态到本地存储
	const saveStateToStorage = () => {
		try {
			// 保存选中的派单ID列表
			uni.setStorageSync(SELECTED_IDS_KEY, JSON.stringify(selectedDeliveryIds.value));

			// 保存手动合并后的派单
			uni.setStorageSync(MANUAL_MERGED_KEY, JSON.stringify(manualMergedDelivery.value));

			// 保存筛选设置
			uni.setStorageSync(FILTER_SETTINGS_KEY, JSON.stringify(filterSettings.value));
		} catch (error) {
			console.warn('保存状态失败:', error);
		}
	};

	// 设置选中的派单ID列表
	const setSelectedDeliveryIds = (ids: string[]) => {
		selectedDeliveryIds.value = ids;
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取选中的派单ID列表
	const getSelectedDeliveryIds = () => selectedDeliveryIds.value;

	// 设置手动合并后的派单
	const setManualMergedDelivery = (delivery: any) => {
		manualMergedDelivery.value = delivery;
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取手动合并后的派单
	const getManualMergedDelivery = () => manualMergedDelivery.value;

	// 设置筛选设置
	const setFilterSettings = (settings: any) => {
		filterSettings.value = { ...filterSettings.value, ...settings };
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取筛选设置
	const getFilterSettings = () => filterSettings.value;

	// 合并当天任务的辅助函数
	const mergeTodayDeliveries = (deliveryList: Delivery[]) => {
		// 按日期分组，同一天的任务合并显示
		const groupedByDate: Record<string, any> = {};

		deliveryList.forEach(item => {
			// 获取日期部分（不含时间）
			const dateStr = item.deliveryDate ? new Date(item.deliveryDate).toISOString().split('T')[0] : 'unknown';

			if (!groupedByDate[dateStr]) {
				// 创建新组
				groupedByDate[dateStr] = {
					...item,
					isGrouped: true,
					groupItems: [item],
					deliveryName: getDeliveryName(item), // 只使用第一个派单的名称
					mergedCount: 1, // 初始化合并数量
				};
			} else {
				// 添加到现有组
				const group = groupedByDate[dateStr];
				group.groupItems.push(item);
				group.mergedCount = group.groupItems.length; // 更新合并数量
			}
		});

		// 转换回数组
		return Object.values(groupedByDate);
	};

	// 根据筛选条件过滤和排序派单列表
	const getFilteredDeliveries = (searchText = '', taskStore?: any) => {
		// 首先应用搜索过滤
		let filtered = deliveries.value;

		// 应用搜索文本过滤
		if (searchText) {
			const searchLower = searchText.toLowerCase();
			filtered = filtered.filter((item: any) => {
				// 搜索派单名称
				if (getDeliveryName(item).toLowerCase().includes(searchLower)) return true;
				// 搜索派单ID
				if (item.deliveryId && item.deliveryId.toString().toLowerCase().includes(searchLower)) return true;
				// 搜索队列ID
				if (item.queueId && item.queueId.toString().toLowerCase().includes(searchLower)) return true;
				return false;
			});
		}

		// 应用完成状态过滤
		const settingsStore = useSettingsStore();
		const completionStatus = settingsStore.settings.deliveryDisplaySettings?.completionStatus || 'incomplete';

		if (completionStatus !== 'all' && taskStore) {
			filtered = filtered.filter((item: any) => {
				// 使用 getDeliveryState 获取准确的任务统计
				const stats = taskStore.getDeliveryState(item.deliveryId);

				// 根据任务统计判断完成状态
				// 完成条件：已传 + 待传 >= 应拍
				const isComplete = stats.requiredCount > 0 &&
					(stats.uploadedCount + stats.pendingCount) >= stats.requiredCount;

				if (completionStatus === 'complete') {
					return isComplete; // 仅显示已完成
				} else if (completionStatus === 'incomplete') {
					return !isComplete; // 仅显示未完成
				}
				return true;
			});
		}

		// 应用排序
		const sorted = [...filtered].sort((a, b) => {
			const dateA = new Date(a.deliveryDate).getTime();
			const dateB = new Date(b.deliveryDate).getTime();
			return filterSettings.value.sortOrder === 'time_asc' ? dateA - dateB : dateB - dateA;
		});

		// 如果选择了手动合并，并且有手动合并的结果，则只显示手动合并的结果
		if (filterSettings.value.mergeType === 'manual_merge' && manualMergedDelivery.value) {
			return [manualMergedDelivery.value];
		}

		// 根据合并类型进行处理
		if (filterSettings.value.mergeType === 'no_merge') {
			// 不合并，直接返回排序后的列表
			return sorted;
		} else if (filterSettings.value.mergeType === 'merge_today') {
			// 合并当天任务
			return mergeTodayDeliveries(sorted);
		} else {
			// 默认返回排序后的列表
			return sorted;
		}
	};

	// 在loadDeliveries后重新计算手动合并的派单
	const recalculateManualMergedDelivery = () => {
		// 如果没有手动合并的派单，直接返回
		if (!manualMergedDelivery.value) return;

		// 获取手动合并的派单中的所有派单ID
		const mergedIds = manualMergedDelivery.value.groupItems.map((item: any) => item.deliveryId);

		// 检查这些ID是否仍然存在于当前的派单列表中
		const existingDeliveries = deliveries.value.filter((item: any) =>
			mergedIds.includes(item.deliveryId)
		);

		// 如果没有找到任何派单，清空手动合并的派单
		if (existingDeliveries.length === 0) {
			manualMergedDelivery.value = null;
			return;
		}

		// 如果找到的派单数量与原来不同，需要重新创建手动合并的派单
		if (existingDeliveries.length !== mergedIds.length) {
			// 创建新的手动合并派单
			const firstDelivery = existingDeliveries[0];
			manualMergedDelivery.value = {
				...firstDelivery,
				deliveryId: `merged_${Date.now()}`, // 生成唯一ID
				isGrouped: true,
				groupItems: existingDeliveries,
				deliveryName: getDeliveryName(firstDelivery),
				mergedCount: existingDeliveries.length,
				deliveryDate: firstDelivery.deliveryDate,
				currentContentImageUrl: firstDelivery.currentContentImageUrl
			};
		}
	};

	// 在loadDeliveries后调用
	const afterLoadDeliveries = () => {
		// 重新计算手动合并的派单
		recalculateManualMergedDelivery();
	};

	// 包装loadDeliveries，集成智能数据管理系统
	const wrappedLoadDeliveries = async (forceLoad = false) => {
		console.info('📋 [DELIVERY] 开始加载派单列表');

		// 先从本地存储加载状态
		loadStateFromStorage();

		// 获取当前用户
		const authStore = useAuthStore();
		const username = authStore.getUsername();

		// 验证用户名是否有效
		if (!username || username.trim() === '') {
			console.error('❌ [DELIVERY] 用户名为空，无法加载派单');
			showToast('用户信息无效，请重新登录', 'none');
			return;
		}

		try {
			// 加载派单数据
			await loadDeliveries(forceLoad);

			// 重新计算手动合并的派单
			afterLoadDeliveries();

			console.info('✅ [DELIVERY] 派单数据加载完成');

			// 派单数据加载完成后，立即加载任务数据以确保统计正确
			console.info('📋 [DELIVERY] 开始加载任务数据以更新统计');
			try {
				const { useTaskStore } = await import('./task');
				const taskStore = useTaskStore();
				await taskStore.loadTasks(forceLoad);
				console.info('✅ [DELIVERY] 任务数据加载完成，统计数据已更新');
			} catch (taskError) {
				console.error('❌ [DELIVERY] 加载任务数据失败:', taskError);
				// 任务数据加载失败不影响派单显示，但统计可能不准确
			}

		} catch (error) {
			console.error('❌ [DELIVERY] 加载派单失败:', error);
			showToast('加载派单失败，请稍后重试', 'none');
		}
	};

	// 优化的 API 方法
	const getFullDelivery = (deliveryId: string): Delivery | undefined => {
		return deliveryDataStorage.getDelivery(deliveryId);
	};

	const getDeliveriesByDate = (date: string): Delivery[] => {
		return deliveryDataStorage.getDeliveriesByDate(date);
	};

	const getDeliveriesByQueue = (queueId: string): Delivery[] => {
		return deliveryDataStorage.getDeliveriesByQueue(queueId);
	};

	const getOptimizedStats = () => readonly(stats);

	const getDeliveryCores = () => readonly(deliveryCores);

	return {
		// ===== 性能优化层 API =====
		deliveryCores: readonly(deliveryCores),
		stats: readonly(stats),
		getFullDelivery,
		getDeliveriesByDate,
		getDeliveriesByQueue,
		getOptimizedStats,
		getDeliveryCores,

		// ===== 兼容层 API（保持现有接口） =====
		deliveries,
		loading,
		refreshing,
		hasData,
		selectedDeliveryIds,
		manualMergedDelivery,
		filterSettings,
		getDeliveryName,
		loadDeliveries: wrappedLoadDeliveries, // 使用包装后的函数
		refreshDeliveries,
		setSelectedDeliveryIds,
		getSelectedDeliveryIds,
		setManualMergedDelivery,
		getManualMergedDelivery,
		setFilterSettings,
		getFilterSettings,
		getFilteredDeliveries,
		mergeTodayDeliveries,
		loadStateFromStorage, // 暴露加载状态方法
		saveStateToStorage, // 暴露保存状态方法
		flushCache, // 缓存管理
		clearCache, // 清除缓存
	};
});
