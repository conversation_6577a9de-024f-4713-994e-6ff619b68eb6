<template>
	<view class="delivery-container" :style="dynamicFontStyle">
		<!-- 派单导航栏 -->
		<delivery-navbar
			:total-count="deliveryStats.total"
			:current-count="deliveryList.length"
			@search="onSearchClick"
			@refresh="onRefreshClick"
			@settings="onSettingsClick" />

		<!-- 筛选选项卡 -->
		<delivery-filter
			:initial-merge-type="filterSettings.mergeType"
			:initial-sort-order="filterSettings.sortOrder"
			@filter-change="handleFilterChange" />

		<!-- 内容区域 -->
		<scroll-view
			scroll-y
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			refresher-threshold="150"
			class="delivery-list"
			:show-scrollbar="false">
			<view v-if="deliveryStats.total === 0" class="empty-state">
				<text>暂无派单信息</text>
			</view>
			<view v-else class="delivery-items" :class="{ 'has-merge-actions': isManualMergeMode && !manualMergedDelivery }">
				<view
					v-for="item in deliveryList"
					:key="item.deliveryId"
					class="delivery-item"
					:class="{ 'checkbox-mode': isManualMergeMode && !manualMergedDelivery }"
					@click="handleDeliveryClick(item)">
					<!-- 手动选择合并模式下显示复选框 -->
					<checkbox
						v-if="isManualMergeMode && !manualMergedDelivery"
						:checked="selectedDeliveryIds.includes(item.deliveryId)"
						class="delivery-checkbox"
						@click.stop="handleDeliveryClick(item)" />

					<!-- 图片显示逻辑：
					     1. 在选择模式下不显示图片
					     2. 在常规模式下，如果是组合派单不显示图片，如果是非组合派单则显示图片并可点击放大
					-->
					<view class="delivery-left" v-if="!isManualMergeMode && !item.isGrouped">
						<smart-image
							v-if="
								item.currentContentImageUrl &&
								item.currentContentImageUrl !== '/static/default-image.svg'
							"
							:src="item.currentContentImageUrl"
							:placeholder-text="'查看派单图片'"
							:image-class="'task-image'"
							:image-style="'width: 100%; height: 100%;'"
							mode="aspectFit"
							@click="(event) => handleImageClick(item, event)"
						/>
						<view
							v-else
							class="default-image-placeholder"
							@click.stop="(event) => handleImageClick(item, event)">
							<text class="default-image-text">空</text>
						</view>
					</view>
					<view class="delivery-right" :class="{ 'full-width': isManualMergeMode || item.isGrouped }">
						<view class="delivery-header">
							<view class="delivery-title">
								<text class="delivery-name" :data-badge="item.isGrouped ? '合并' : ''">
									{{ item.displayName || item.deliveryName || item.name }}
									<!-- 显示合并的派单数量（新格式） -->
									<text v-if="item.isGrouped && item.groupItems" class="merged-count">
										等
										<text class="count-number">{{
											item.mergedCount || item.groupItems.length
										}}</text>
										个派单
									</text>
								</text>
							</view>
						</view>
						<view class="delivery-stats">
							<!-- 两栏布局：左边点位，右边实景 -->
							<view class="stats-columns">
								<!-- 左栏：点位统计 -->
								<view class="stats-column left-column">
									<view class="column-header">
										<text class="column-title">
											<text class="column-icon iconfont icon-point"></text>
											<text class="column-text">点位</text>
										</text>
									</view>
									<view class="column-stats">
										<text class="status-item highlight-stats-item compact"
											><text class="stats-label-highlight">总数量:</text>
											<text class="status-value stats-value-highlight total">{{ getDeliveryStats(item).spotTotalCount }}</text></text
										>
										<text class="status-item highlight-stats-item compact"
											><text class="stats-label-highlight">已完成:</text>
											<text class="status-value stats-value-highlight completed">{{ getDeliveryStats(item).spotCompletedCount }}</text></text
										>
										<text class="status-item highlight-stats-item compact"
											:class="{
												'zero-uncompleted': getDeliveryStats(item).spotUncompleteCount === 0
											}">
											<text class="stats-label-highlight"
												:class="{
													'zero-uncompleted-label': getDeliveryStats(item).spotUncompleteCount === 0
												}">未完成:</text>
											<text class="status-value stats-value-highlight warning-value uncompleted"
												:class="{
													'zero-uncompleted-value': getDeliveryStats(item).spotUncompleteCount === 0
												}">{{ getDeliveryStats(item).spotUncompleteCount }}</text></text
										>
									</view>
								</view>

								<!-- 右栏：实景统计 -->
								<view class="stats-column right-column">
									<view class="column-header">
										<text class="column-title">
											<text class="column-icon iconfont icon-house"></text>
											<text class="column-text">实景</text>
										</text>
									</view>
									<view class="column-stats">
										<text class="status-item highlight-stats-item compact"
											><text class="stats-label-highlight">总数量:</text>
											<text class="status-value stats-value-highlight total">{{ getDeliveryStats(item).zoneTotalCount }}</text></text
										>
										<text class="status-item highlight-stats-item compact"
											><text class="stats-label-highlight">已完成:</text>
											<text class="status-value stats-value-highlight completed">{{ getDeliveryStats(item).zoneCompletedCount }}</text></text
										>
										<text class="status-item highlight-stats-item compact"
											:class="{
												'zero-uncompleted': getDeliveryStats(item).zoneUncompleteCount === 0
											}">
											<text class="stats-label-highlight"
												:class="{
													'zero-uncompleted-label': getDeliveryStats(item).zoneUncompleteCount === 0
												}">未完成:</text>
											<text class="status-value stats-value-highlight warning-value uncompleted"
												:class="{
													'zero-uncompleted-value': getDeliveryStats(item).zoneUncompleteCount === 0
												}">{{ getDeliveryStats(item).zoneUncompleteCount }}</text></text
										>
									</view>
								</view>
							</view>
						</view>
						<view class="delivery-info">
							<text class="task-time">任务时间: {{ formatDate(item.deliveryDate) }}</text>
							<text v-if="item.queueId" class="queue-id">队列: {{ item.queueId }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 手动选择合并模式下的底部操作栏（仅在选择阶段显示） -->
		<view v-if="isManualMergeMode && !manualMergedDelivery" class="merge-actions">
			<view class="action-button cancel" @click="cancelManualMerge">
				<text class="action-icon iconfont icon-cancel"></text>
				<text class="action-text">取消</text>
			</view>
			<view class="action-button confirm" @click="confirmManualMerge">
				<text class="action-icon iconfont icon-confirm"></text>
				<text class="action-text">确定</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, watch, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useDeliveryStore, useTaskStore, useAuthStore, useSettingsStore } from '@/stores';
import { useOptimizedDeliveryList } from '@/composables/useOptimizedDeliveryList';
import { formatDate, showToast } from '@/utils';
import DeliveryNavbar from './components/delivery-navbar.vue';
import DeliveryFilter from './components/delivery-filter.vue';
import SmartImage from '../../components/smart-image.vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

const deliveryStore = useDeliveryStore();
const taskStore = useTaskStore();
const { refreshing, selectedDeliveryIds, manualMergedDelivery, filterSettings } = storeToRefs(deliveryStore);
const {
	loadDeliveries,
	refreshDeliveries,
	getDeliveryName,
	setSelectedDeliveryIds,
	setManualMergedDelivery,
	setFilterSettings,
} = deliveryStore;
const { getDeliveryState } = taskStore;

// ===== 优化的派单列表 =====
const optimizedDeliveryList = useOptimizedDeliveryList();
const {
	deliveryCores,
	filteredDeliveryCores,
	searchFilteredDeliveryCores,
	processedDeliveryCores,
	getDisplayDataList,
	stats: deliveryStats,
	getDateGroupStats,
	getFullDelivery,
	getDeliveryDisplayData
} = optimizedDeliveryList;

// 性能监控
const performanceMonitor = {
	renderStart: 0,
	renderEnd: 0,
	lastRenderTime: 0,

	startRender() {
		this.renderStart = typeof performance !== 'undefined' ? performance.now() : Date.now();
	},

	endRender() {
		this.renderEnd = typeof performance !== 'undefined' ? performance.now() : Date.now();
		this.lastRenderTime = this.renderEnd - this.renderStart;
		console.info(`🚀 [PERFORMANCE] 派单列表渲染耗时: ${this.lastRenderTime.toFixed(2)}ms`);
	}
};

// 获取派单统计数据（使用优化的数据）
const getDeliveryStats = (item: any) => {
	try {
		// 直接使用优化数据中的统计信息
		if (item.stats) {
			return {
				zoneTotalCount: item.stats.zoneTotalCount || 0,
				zoneCompletedCount: item.stats.zoneCompletedCount || 0,
				zoneUncompleteCount: item.stats.zoneUncompleteCount || 0,
				spotTotalCount: item.stats.spotTotalCount || 0,
				spotCompletedCount: item.stats.spotCompletedCount || 0,
				spotUncompleteCount: item.stats.spotUncompleteCount || 0
			};
		}

		// 如果没有预计算的统计信息，则使用传统方式
		let result;
		if (item.isGrouped && item.groupItems) {
			// 合并派单：收集所有deliveryId，一次性调用getDeliveryState
			const deliveryIds = item.groupItems.map((groupItem: any) => groupItem.deliveryId);
			result = getDeliveryState(deliveryIds);
		} else {
			// 单个派单：直接传递deliveryId
			result = getDeliveryState(item.deliveryId);
		}

		// 只在统计数据全为0时输出调试信息
		if (result.zoneTotalCount === 0 && result.spotTotalCount === 0) {
			console.log(`📊 [DELIVERY_STATS] 派单 ${item.deliveryId} 统计为0，可能需要检查任务数据`);
		}

		return result;
	} catch (error) {
		console.error('❌ getDeliveryStats error:', error, 'item:', item);
		return {
			zoneTotalCount: 0,
			zoneCompletedCount: 0,
			zoneUncompleteCount: 0,
			spotTotalCount: 0,
			spotCompletedCount: 0,
			spotUncompleteCount: 0
		};
	}
};

// 下拉菜单是否显示
const dropdownVisible = ref(false);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
	'--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
	try {
		const savedFontScale = uni.getStorageSync('fontScale') || 1;
		currentFontScale.value = savedFontScale;
		console.info(`📄 [DELIVERY] 页面字体大小初始化: 缩放 ${savedFontScale}`);
	} catch (error) {
		console.error('页面字体大小初始化失败:', error);
	}
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
	const { size, scale } = data;
	currentFontScale.value = scale;
	console.info(`📄 [DELIVERY] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 监听任务完成事件
onMounted(async () => {
	// 初始化字体大小
	initFontSize();

	// 监听字体大小变化
	uni.$on('fontSizeChanged', handleFontSizeChange);

	// 确保任务数据已加载
	console.info('📋 [DELIVERY] 页面加载，检查任务数据状态');
	const taskCount = taskStore.tasks.length;
	console.info(`📋 [DELIVERY] 当前任务数量: ${taskCount}`);

	if (taskCount === 0) {
		console.info('📋 [DELIVERY] 任务数据为空，尝试加载任务数据');
		try {
			await taskStore.loadTasks();
			console.info(`📋 [DELIVERY] 任务数据加载完成，任务数量: ${taskStore.tasks.length}`);

			// 任务数据加载完成后，主动触发派单统计更新
			optimizedDeliveryList.updateDeliveryStats();
			console.info('📋 [DELIVERY] 任务数据加载完成，已触发派单统计更新');
		} catch (error) {
			console.error('📋 [DELIVERY] 任务数据加载失败:', error);
		}
	} else {
		// 即使有任务数据，也主动触发一次统计更新，确保数据同步
		optimizedDeliveryList.updateDeliveryStats();
		console.info('📋 [DELIVERY] 页面加载完成，已触发派单统计更新');
	}

	// 监听单个任务完成事件 - 主动触发统计更新
	uni.$on('taskCompleted', (data: any) => {
		console.info('🎉 [TASK_COMPLETED] 收到任务完成事件:', data);
		// 主动触发派单统计更新
		if (data.deliveryId) {
			// 通知优化的派单列表更新指定派单的统计
			optimizedDeliveryList.updateDeliveryStats([data.deliveryId]);
		}
	});

	// 监听批量任务完成事件 - 主动触发统计更新
	uni.$on('batchTaskCompleted', (data: any) => {
		console.info('🎉 [BATCH_COMPLETED] 收到批量任务完成事件:', data);
		// 主动触发派单统计更新
		if (data.deliveryIds && Array.isArray(data.deliveryIds)) {
			// 通知优化的派单列表更新指定派单的统计
			optimizedDeliveryList.updateDeliveryStats(data.deliveryIds);
		}
	});

	// 监听拍照事件 - 主动触发统计更新
	uni.$on('photoTaken', (data: any) => {
		console.info('📸 [PHOTO_TAKEN] 收到拍照事件:', data);
		if (data.deliveryId) {
			optimizedDeliveryList.updateDeliveryStats([data.deliveryId]);
		}
	});

	// 监听上传事件 - 主动触发统计更新
	uni.$on('photoUploaded', (data: any) => {
		console.info('📤 [PHOTO_UPLOADED] 收到上传事件:', data);
		if (data.deliveryId) {
			optimizedDeliveryList.updateDeliveryStats([data.deliveryId]);
		}
	});

	// 监听派单加载完成事件 - 加载任务数据
	uni.$on('deliveryLoadCompleted', async (data: any) => {
		console.info('📋 [DELIVERY_LOAD_COMPLETED] 收到派单加载完成事件:', data);
		try {
			await taskStore.loadTasks(data.forceLoad);
			console.info('✅ [DELIVERY_LOAD_COMPLETED] 任务数据加载完成');
			// 任务数据加载完成后，主动触发派单统计更新
			optimizedDeliveryList.updateDeliveryStats();
		} catch (error) {
			console.error('❌ [DELIVERY_LOAD_COMPLETED] 任务数据加载失败:', error);
		}
	});
});

onUnmounted(() => {
	// 清理事件监听
	uni.$off('fontSizeChanged', handleFontSizeChange);
	uni.$off('taskCompleted');
	uni.$off('batchTaskCompleted');
	uni.$off('photoTaken');
	uni.$off('photoUploaded');
	uni.$off('deliveryLoadCompleted');
});

// 手动选择合并相关状态
const isManualMergeMode = ref(false); // 是否处于手动选择合并模式
const actualMergeType = ref(filterSettings.value.mergeType); // 实际的合并类型，初始值与当前合并类型一致

// 处理筛选变更
const handleFilterChange = (filterData: any) => {
	if (filterData.tabIndex === 0) {
		// 合并类型变更
		setFilterSettings({ mergeType: filterData.value });

		if (filterData.value === 'manual_merge') {
			// 当选择"手动合并"时

			// 如果已经有手动合并的结果，则直接显示手动合并的结果，不进入选择模式
			if (manualMergedDelivery.value) {
				// 设置实际类型为手动合并
				actualMergeType.value = 'manual_merge';
				// 不进入选择模式
				isManualMergeMode.value = false;
				// 清空选择
				setSelectedDeliveryIds([]);
			} else {
				// 如果没有手动合并的结果，则进入选择模式
				isManualMergeMode.value = true;
				// 清空选择
				setSelectedDeliveryIds([]);
				// 保持当前的合并类型，只是进入选择模式
				// 注意：这里不改变 actualMergeType，保持之前的合并类型
			}
		} else if (isManualMergeMode.value) {
			// 如果已经在选择模式下，并选择了其他合并类型
			// 只更新实际的合并类型，但保持在选择模式
			actualMergeType.value = filterData.value;

			// 注意：不退出选择模式，只有通过确认或取消按钮才能退出
		} else {
			// 常规模式下的处理
			// 如果有手动合并的派单，清除它（除非选择的是手动合并）
			if (manualMergedDelivery.value && filterData.value !== 'manual_merge') {
				setManualMergedDelivery(null);
			}

			// 确保不在选择模式
			isManualMergeMode.value = false;

			// 清空选中的派单ID
			setSelectedDeliveryIds([]);

			// 更新实际的合并类型
			actualMergeType.value = filterData.value;
		}
	} else if (filterData.tabIndex === 1) {
		// 排序方式变更
		setFilterSettings({ sortOrder: filterData.value });
		// 主动触发统计更新
		optimizedDeliveryList.updateDeliveryStats();
		console.info('📊 [FILTER] 排序方式变更，更新派单统计');
	}
};

// 搜索文本
const searchText = ref('');

// 派单列表数据（直接使用优化的显示数据）
const deliveryList = computed(() => {
	performanceMonitor.startRender();
	nextTick(() => {
		performanceMonitor.endRender();
	});

	return getDisplayDataList.value(searchText.value);
});

// 监控派单列表的变化
watch(deliveryList, () => {
	performanceMonitor.startRender();
	nextTick(() => {
		performanceMonitor.endRender();
	});
}, { immediate: false });

// 处理派单点击事件
const handleDeliveryClick = (item: any) => {
	// 如果处于手动选择合并模式，点击派单时切换选中状态
	if (isManualMergeMode.value && !manualMergedDelivery.value) {
		// 切换选中状态
		const index = selectedDeliveryIds.value.indexOf(item.deliveryId);
		if (index === -1) {
			// 如果未选中，则添加到选中列表
			const newIds = [...selectedDeliveryIds.value, item.deliveryId];
			setSelectedDeliveryIds(newIds);
		} else {
			// 如果已选中，则从选中列表中移除
			const newIds = [...selectedDeliveryIds.value];
			newIds.splice(index, 1);
			setSelectedDeliveryIds(newIds);
		}
		return;
	}

	// 检查是否是合并的任务组
	if (item.isGrouped && item.groupItems && item.groupItems.length > 0) {
		// 获取所有派单ID
		const deliveryIds = item.groupItems.map((groupItem: any) => groupItem.deliveryId);

		// 更新选中的派单ID列表
		taskStore.setSelectedDeliveryIds(deliveryIds);

		// 使用switchTab跳转到任务页面
		uni.switchTab({
			url: '/pages/task/index',
		});
	} else {
		// 普通任务，直接跳转
		// 更新选中的派单ID
		taskStore.setSelectedDeliveryIds([item.deliveryId]);
		// 使用switchTab跳转到任务页面
		uni.switchTab({
			url: '/pages/task/index',
		});
	}
};

// 下拉刷新
const onRefresh = async () => {
	await refreshDeliveries();
};

// 处理图片加载错误
const handleImageError = (item: any) => {
	// 将图片URL设置为null，触发使用默认图标占位符
	if (item.currentContentImageUrl) {
		item.currentContentImageUrl = null;
	}
};

// 处理图片点击，放大查看
const handleImageClick = (item: any, event: any) => {
	// 阻止事件冒泡，避免触发派单点击事件
	event.stopPropagation();

	// 如果是组合派单或没有图片，不执行任何操作
	if (item.isGrouped || !item.currentContentImageUrl) {
		return;
	}

	// 使用uni.previewImage预览图片
	uni.previewImage({
		urls: [item.currentContentImageUrl],
		current: item.currentContentImageUrl,
		indicator: 'number',
		loop: false,
	});
};

// 处理下拉菜单点击
const toggleDropdown = () => {
	dropdownVisible.value = !dropdownVisible.value;
	// 这里可以添加下拉菜单的显示逻辑
};

// 处理搜索
const onSearchClick = (text: string) => {
	searchText.value = text;
};

// 处理刷新按钮点击
const onRefreshClick = async () => {
	// 显示加载中提示
	showToast('正在刷新数据...', 'loading');

	try {
		// 加载派单数据（会自动触发任务数据加载）
		await loadDeliveries(true);

		// 显示成功提示
		showToast('数据刷新成功', 'success');
		console.info('📋 [DELIVERY] 数据刷新完成');
	} catch (error) {
		// 显示错误提示
		console.error('刷新数据失败:', error);
		showToast('刷新数据失败，请重试', 'none');
	}
};

// 处理设置按钮点击
const onSettingsClick = () => {
	console.info('🔧 [DELIVERY-INDEX] 主页面收到设置按钮点击事件');
	// 获取当前设置作为基准
	const settingsStore = useSettingsStore();
	const originalOrderPeriod = settingsStore.settings.orderPeriod || 'week';

	// 跳转到派单设置页面
	uni.navigateTo({
		url: '/pages/delivery/delivery-settings',
		events: {
			// 监听设置结果
			settingsResult: (data: any) => {
				console.info('收到派单设置结果:', data);
				// 处理设置结果
				if (data.orderPeriod) {
					// 只有当派单期间范围实际发生变化时，才重新加载派单数据
					if (data.orderPeriod !== originalOrderPeriod) {
						console.info(`派单期间范围已更新: ${originalOrderPeriod} → ${data.orderPeriod}，重新加载派单数据`);
						loadDeliveries(true); // 强制重新加载
					} else {
						console.info('派单期间范围未变化，无需重新加载数据');
					}
				}
				// 完成状态过滤会在 getFilteredDeliveries 中自动应用
				// 因为它读取的是 settingsStore 中的最新设置
			}
		},
		success: (res: any) => {
			// 获取当前设置
			const currentCompletionStatus = settingsStore.settings.deliveryDisplaySettings?.completionStatus || 'incomplete';
			const currentOrderPeriod = settingsStore.settings.orderPeriod || 'week';

			// 通过事件通道传递当前设置
			res.eventChannel.emit('currentSettings', {
				completionStatus: currentCompletionStatus,
				orderPeriod: currentOrderPeriod
			});
		}
	});
};

// 取消手动合并
const cancelManualMerge = () => {
	// 清除选中状态
	setSelectedDeliveryIds([]);

	// 如果已经有合并后的派单，清除它
	if (manualMergedDelivery.value) {
		setManualMergedDelivery(null);
	}

	// 退出手动选择合并模式
	isManualMergeMode.value = false;

	// 将合并类型设置为实际选择的合并类型
	setFilterSettings({ mergeType: actualMergeType.value });

	// 重置实际合并类型
	actualMergeType.value = 'no_merge';
};

// 确认手动合并
const confirmManualMerge = () => {
	// 检查是否有选中的派单
	if (selectedDeliveryIds.value.length === 0) {
		showToast('请至少选择一个派单', 'none');
		return;
	}

	// 获取选中的派单
	const selectedDeliveries = selectedDeliveryIds.value.map(id => getFullDelivery(id)).filter(Boolean);

	// 创建合并后的派单
	const firstDelivery = selectedDeliveries[0];

	// 如果只选择了一个派单，直接使用该派单，但标记为手动合并
	if (selectedDeliveries.length === 1) {
		const mergedDelivery = {
			...firstDelivery,
			deliveryId: `merged_${Date.now()}`, // 生成唯一ID
			isGrouped: true,
			groupItems: selectedDeliveries,
			deliveryName: getDeliveryName(firstDelivery),
			mergedCount: 1, // 只有一个派单
			deliveryDate: firstDelivery.deliveryDate,
			currentContentImageUrl: firstDelivery.currentContentImageUrl,
		};

		// 设置手动合并的派单
		setManualMergedDelivery(mergedDelivery);

		// 显示成功提示
		showToast('已选择派单', 'success');
	} else {
		// 多个派单合并
		const mergedDelivery = {
			...firstDelivery,
			deliveryId: `merged_${Date.now()}`, // 生成唯一ID
			isGrouped: true,
			groupItems: selectedDeliveries,
			deliveryName: getDeliveryName(firstDelivery), // 只使用第一个派单的名称，数量在UI中显示
			mergedCount: selectedDeliveries.length, // 添加合并数量属性
			deliveryDate: firstDelivery.deliveryDate,
			currentContentImageUrl: firstDelivery.currentContentImageUrl,
		};

		// 设置手动合并的派单
		setManualMergedDelivery(mergedDelivery);

		// 显示合并成功提示
		showToast(`已合并 ${selectedDeliveries.length} 个派单`, 'success');
	}

	// 退出手动选择合并模式
	isManualMergeMode.value = false;

	// 将合并类型设置为手动合并
	setFilterSettings({ mergeType: 'manual_merge' });
};

onMounted(async () => {
	// 检查登录状态
	const authStore = useAuthStore();
	if (!(await authStore.checkAuth())) {
		uni.reLaunch({
			url: '/pages/login/login',
		});
		return;
	}

	// 加载派单数据
	await loadDeliveries();

	// 加载任务数据，用于统计
	await taskStore.loadTasks();

	// 数据加载完成后，主动更新派单统计
	optimizedDeliveryList.updateDeliveryStats();
	console.info('📊 [PAGE_LOAD] 派单页面数据加载完成，更新统计');
});

onUnmounted(() => {
	// 强制保存缓存
	if (deliveryStore.flushCache) {
		deliveryStore.flushCache();
	}
	if (taskStore.flushCache) {
		taskStore.flushCache();
	}
});
</script>

<style lang="scss" scoped>
.delivery-container {
	min-height: 100vh;
	background-color: $uni-bg-color-grey;
	width: 100%;
	box-sizing: border-box;
	overflow-x: hidden;
	position: relative; // 为底部操作栏提供定位上下文

	// 应用字体缩放
	font-size: calc(16px * var(--font-scale, 1));

	// 筛选选项卡定位
	:deep(.filter-tabs) {
		position: fixed;
		top: calc(44px + var(--status-bar-height)); // 导航栏高度 + 状态栏高度
		left: 0;
		right: 0;
		z-index: 99;
		width: 100%;
	}

	// 内容区域需要留出顶部导航栏和筛选栏的空间
	.delivery-list {
		height: calc(
			100vh - 44px - var(--status-bar-height) - 50px - 50px
		); // 导航栏高度(44px) + 状态栏高度 + 筛选栏高度(50px) + 底部导航栏高度(50px)
		margin-top: calc(44px + var(--status-bar-height) + 50px); // 导航栏高度 + 状态栏高度 + 筛选栏高度
		width: 100%;
		box-sizing: border-box;
		padding: 0;

		// 隐藏滚动条但保留滚动功能
		&::-webkit-scrollbar {
			display: none; // 对于 WebKit 浏览器
		}

		scrollbar-width: none; // 对于 Firefox
		-ms-overflow-style: none; // 对于 IE 和 Edge
	}

	.empty-state {
		padding: 2.5rem;
		text-align: center;
		color: $uni-text-color-grey;
		font-size: 1.125rem;
	}

	.delivery-items {
		padding: 0;
		margin: 0;
		width: 100%;
		padding-bottom: 20px; // 默认底部空间

		// 当显示 merge-actions 时，增加底部空间
		&.has-merge-actions {
			padding-bottom: 80px; // 为 merge-actions (60px) + 额外空间 (20px)
		}
	}

	.delivery-item {
		background-color: $uni-bg-color;
		padding: 1.5rem 0.1rem;
		margin-top: 0.2rem;
		margin-bottom: 0.1rem;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: stretch;
		width: 100%;
		min-height: 8rem;
		position: relative; // 为复选框提供定位上下文

		// 为复选框留出空间的类
		&.checkbox-mode {
			padding-left: 3rem;
		}

		// 复选框样式
		.delivery-checkbox {
			position: absolute;
			left: 1rem;
			top: 50%;
			transform: translateY(-50%);
			z-index: 2;
			transform-origin: center;
			width: 1.5rem;
			height: 1.5rem;
			border-radius: 50%;
			background-color: rgba(255, 255, 255, 0.8); // 添加背景色，使复选框更加明显
			padding: 0.25rem;
			box-sizing: content-box;
			box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); // 添加阴影，使复选框更加突出
		}

		.delivery-left {
			position: relative;
			width: 20%;
			/* 图片占整体宽度的20% */
			min-width: 5rem;
			max-width: 7.5rem;
			margin: 0 0.75rem;
			display: flex;
			align-items: center;
			justify-content: center;

			.task-image {
				width: 100%;
				height: auto;
				border-radius: 0.25rem;
				aspect-ratio: 1/1; /* 保持正方形容器 */
				object-fit: contain; /* 保持图片原始比例 */
			}

			.default-image-placeholder {
				width: 100%;
				height: auto;
				border-radius: 0.25rem;
				aspect-ratio: 1/1; /* 保持正方形容器 */
				background-color: #ffffff;
				border: 1px solid #e0e0e0;
				display: flex;
				align-items: center;
				justify-content: center;

				.default-image-text {
					font-size: 80rpx;
					color: #e0e0e0;
				}
			}
		}

		.delivery-right {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			// 当处于手动选择合并模式时，占据全宽
			&.full-width {
				width: 100%;
				padding-left: 1rem;
			}

			.delivery-header {
				margin-bottom: 0.5rem;

				.delivery-title {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					gap: 0.25rem;
				}

				.delivery-name {
					font-weight: 700;
					color: $uni-text-color;
					line-height: 1.3;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;

					&::after {
						content: attr(data-badge);
						display: inline-block;
						color: #ffffff;
						background-color: #0cbfb0;
						border-radius: 0.25rem;
						padding: 0 0.25rem;
						margin-left: 0.25rem;
						vertical-align: middle;
						font-weight: normal;
						line-height: 1.2;
					}
				}

				.queue-badge {
					display: inline-block;
					color: #ffffff;
					background-color: #ff6b00;
					border-radius: 0.25rem;
					padding: 0 0.25rem;
					font-weight: normal;
					line-height: 1.2;
					font-size: 0.75rem;
				}
			}

			.delivery-stats {
				padding-right: 0.8rem;
				margin-bottom: 0.5rem;

				.stats-columns {
					display: flex;
					gap: 0.5rem;

					.stats-column {
						flex: 1;
						background-color: #f8f9fa;
						border-radius: 8px;
						padding: 8px;
						border: 1px solid #e9ecef;

						.column-header {
							text-align: center;
							margin-bottom: 6px;
							padding-bottom: 4px;
							border-bottom: 1px solid #dee2e6;

							.column-title {
								font-weight: 800;
								color: #495057;
								font-size: 0.85em;
								background-color: #e9ecef;
								padding: 2px 8px;
								border-radius: 4px;
								display: inline-flex;
								align-items: center;
								gap: 4px;

								.column-icon {
									font-size: 16px;
									margin-right: 2px;
								}

								.column-text {
									font-weight: 800;
								}
							}
						}

						.column-stats {
							display: flex;
							flex-direction: column;
							gap: 4px;
						}

						// 左栏（点位）样式
						&.left-column {
							.column-title {
								background-color: rgba(13, 110, 253, 0.1);
								color: #0d6efd;
							}
						}

						// 右栏（实景）样式
						&.right-column {
							.column-title {
								background-color: rgba(111, 66, 193, 0.1);
								color: #6f42c1;
							}
						}
					}
				}

				.stats-row {
					display: flex;
					flex-wrap: wrap;
					margin-bottom: 0.25rem;
				}

				.status-item {
					margin-right: 0.75rem;
					color: $uni-text-color;
					display: inline-flex;
					align-items: center;
					flex-wrap: nowrap;

					.status-value {
						font-weight: 700;
						margin-left: 2px;
						color: $uni-text-color;

						&.warning-value {
							color: #ff0000;
						}
					}

					// 突出显示统计项
					&.highlight-stats-item {
						background-color: #f8f9fa;
						padding: 6px 10px;
						border-radius: 6px;
						margin-right: 8px;
						margin-bottom: 4px;
						border-left: 3px solid #e9ecef;
						box-shadow: 0 1px 3px rgba(0,0,0,0.1);

						.stats-label-highlight {
							font-weight: 800;
							color: #495057;
							font-size: 0.9em;
							margin-right: 4px;
						}

						.stats-value-highlight {
							font-weight: 900;
							font-size: 1.1em;
							text-shadow: 0 1px 2px rgba(0,0,0,0.1);
							padding: 2px 6px;
							border-radius: 4px;

							// 总数 - 蓝色
							&.total {
								color: #0d6efd;
								background-color: rgba(13, 110, 253, 0.15);
							}

							// 已完成 - 绿色
							&.completed {
								color: #198754;
								background-color: rgba(25, 135, 84, 0.15);
							}

							// 未完成 - 红色
							&.uncompleted {
								color: #dc3545;
								background-color: rgba(220, 53, 69, 0.15);
							}
						}

						// 紧凑模式样式（用于两栏布局）
						&.compact {
							background-color: transparent;
							padding: 3px 6px;
							margin-right: 0;
							margin-bottom: 2px;
							border-left: none;
							box-shadow: none;
							border-radius: 4px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.stats-label-highlight {
								font-size: 0.8em;
								text-align: left;
								flex-shrink: 0;
							}

							.stats-value-highlight {
								font-size: 1em;
								text-shadow: 0 1px 2px rgba(0,0,0,0.1);
								text-align: center;
								flex: 1;
								margin-left: 8px;

								// 在紧凑模式下使用更鲜艳的颜色，但不要底色
								&.total {
									color: #0056b3;
								}

								&.completed {
									color: #155724;
								}

								&.uncompleted {
									color: #721c24;
								}
							}
						}

						// 不同类型的边框颜色（仅非紧凑模式）
						&:not(.compact) {
							&:has(.total) {
								border-left-color: #0d6efd;
							}

							&:has(.completed) {
								border-left-color: #198754;
							}

							&:has(.uncompleted) {
								border-left-color: #dc3545;
							}
						}

						// 未完成数量为 0 时的灰色样式
						&.zero-uncompleted {
							.stats-label-highlight.zero-uncompleted-label {
								color: #999999 !important; /* 未完成为0时，标签显示灰色 */
							}

							.stats-value-highlight.zero-uncompleted-value {
								color: #999999 !important; /* 未完成为0时，数字显示灰色 */
								background-color: transparent !important; /* 去掉背景色 */
							}
						}
					}
				}
			}

			.delivery-info {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.task-time {
					color: $uni-text-color-grey;
					padding: 2px 0;
				}

				.queue-id {
					color: $uni-text-color-grey;
					padding: 2px 0;
					font-size: 0.85em;
				}
			}
		}
	}

	// 底部操作栏样式
	.merge-actions {
		position: fixed;
		bottom: var(--window-bottom); // 考虑底部安全区域
		left: 0;
		right: 0;
		height: 60px;
		background-color: #ffffff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		z-index: 9999; // 确保显示在 Tab 栏上方

		.action-button {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: row; // 改为水平布局
			justify-content: center;
			align-items: center;
			padding: 0 20px;
			transition: all 0.2s ease;

			&:active {
				opacity: 0.7;
			}

			&.cancel {
				color: #ff5252;
				border-right: 1px solid #eeeeee;

				.action-icon {
					color: #ff5252;
				}
			}

			&.confirm {
				color: #0cbfb0;

				.action-icon {
					color: #0cbfb0;
				}
			}

			&.back {
				color: #2196f3;

				.action-icon {
					color: #2196f3;
				}
			}

			.action-icon {
				font-size: 16px;
				margin-right: 0.5rem; // 图标和文字之间的间距
			}

			.action-text {
				font-size: 1rem;
				font-weight: 500; // 稍微加粗
			}
		}
	}

	// 合并派单数量样式（新格式）
	.merged-count {
		font-size: 0.9rem;
		color: $uni-text-color;
		margin-left: 0.25rem;
		font-weight: normal;

		.count-number {
			color: #ff0000; // 红色显示数量
			font-weight: bold;
		}
	}
}
</style>
