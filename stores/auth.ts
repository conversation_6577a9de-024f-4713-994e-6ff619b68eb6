import { defineStore } from 'pinia';
import { ref } from 'vue';
import { AuthResponse, LoginCredentials, UserInfo } from '@/types';
import { showToast } from '@/utils/toast';
import { AuthApi } from '@/utils/api';
import { useDeliveryStore } from './delivery';
import { useTaskStore } from './task';
import { useImagesStore } from './images';

// 存储键名常量
const TOKEN_KEY = 'token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'userInfo';
const CREDENTIALS_KEY = 'credentials';

export const useAuthStore = defineStore('auth', () => {
  // 认证状态
  const isAuthenticated = ref(false);

  // 获取用户信息
  const getUserInfo = async (): Promise<UserInfo | null> => {
    try {
      console.info('🔍 [AUTH] 开始获取用户信息');
      const userInfo = await AuthApi.getUserInfo();
      console.info('✅ [AUTH] 用户信息获取成功:', userInfo);
      return userInfo;
    } catch (error) {
      console.error('❌ [AUTH] 获取用户信息失败:', error);
      return null;
    }
  };

  // 保存认证信息
  const saveTokanThenLoadUserInfo = async (authData: AuthResponse) => {
    uni.setStorageSync(TOKEN_KEY, authData.access_token);
    uni.setStorageSync(REFRESH_TOKEN_KEY, authData.refresh_token);

    // 获取用户详细信息
    const userInfo = await getUserInfo();
    if (userInfo) {
      // 保存用户信息，包括头像
      uni.setStorageSync(USER_KEY, {
        ...userInfo,
        nickname: userInfo.roles.find(r => !r.startsWith('ROLE_')) || userInfo.sub,
        avatar: userInfo.avatar || '/static/login-logo.svg' // 如果没有头像则使用默认头像
      });
    } else {
      console.error('获取用户信息失败');
    }

    isAuthenticated.value = true;
  };

  // 获取认证信息
  const getAuth = () => {
    const token = uni.getStorageSync(TOKEN_KEY);
    const refreshToken = uni.getStorageSync(REFRESH_TOKEN_KEY);
    const user = uni.getStorageSync(USER_KEY);
    return { token, refreshToken, user };
  };

  // 清除认证信息
  const clearAuth = () => {
    uni.removeStorageSync(TOKEN_KEY);
    uni.removeStorageSync(REFRESH_TOKEN_KEY);
    uni.removeStorageSync(USER_KEY);
    uni.removeStorageSync(CREDENTIALS_KEY);
    isAuthenticated.value = false;
  };

  // 检查是否已认证
  const checkAuth = async (): Promise<boolean> => {
    const token = uni.getStorageSync(TOKEN_KEY);
    if (token) {
      isAuthenticated.value = true;
      return true;
    }

    // 尝试自动登录
    const credentials = uni.getStorageSync(CREDENTIALS_KEY);
    if (credentials?.username && credentials?.password) {
      return await login(credentials);
    }

    isAuthenticated.value = false;
    return false;
  };

  // 加载用户数据（delivery和task）
  const loadUserData = async () => {
    const deliveryStore = useDeliveryStore();
    const taskStore = useTaskStore();

    await Promise.all([
      deliveryStore.loadDeliveries(true),
      taskStore.loadTasks(true)
    ]);
  };

  // 后台加载用户数据（不阻塞页面跳转）
  const loadUserDataInBackground = () => {
    console.info('📊 [AUTH] 开始后台加载用户数据');
    loadUserData().then(() => {
      console.info('🎉 [AUTH] 后台数据加载完成');
    }).catch((error) => {
      console.error('❌ [AUTH] 后台数据加载失败:', error);
    });
  };

  // 登录方法
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      console.info(`🔐 [AUTH] 开始登录: 用户=${credentials.username}`);
      const response = await AuthApi.login(credentials);

      if (response) {
        console.info('✅ [AUTH] 登录成功，开始保存认证信息');

        // 清除所有缓存数据（避免不同用户数据混淆）
        clearAllCache();

        // 保存认证信息
        await saveTokanThenLoadUserInfo(response);
        // 保存登录凭证用于自动登录
        uni.setStorageSync(CREDENTIALS_KEY, credentials);
        // 显示登录成功提示
        showToast('登录成功', 'success');

        // 立即返回成功，让页面跳转
        console.info('✅ [AUTH] 登录流程完成，开始后台加载数据');

        // 后台加载用户数据（不阻塞页面跳转）
        loadUserDataInBackground();

        return true;
      }
      console.warn('⚠️ [AUTH] 登录响应为空');
      showToast('登录失败，请检查用户名和密码', 'none');
      return false;
    } catch (error) {
      console.error('❌ [AUTH] 登录失败:', error);
      showToast('登录失败，请稍后重试', 'none');
      return false;
    }
  };

  // 登出方法
  const logout = async () => {
    try {
      const { token } = getAuth();
      if (token) {
        await AuthApi.logout(token);
      }
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      // 清除认证信息
      clearAuth();

      // 清除所有缓存数据
      clearAllCache();

      uni.reLaunch({
        url: '/pages/login/login'
      });
    }
  };

  // 获取用户名
  const getUsername = (): string => {
    const user = uni.getStorageSync(USER_KEY);
    return user?.sub || '';
  };

  // 获取昵称
  const getNickname = (): string => {
    const user = uni.getStorageSync(USER_KEY);
    return user?.nickname || user?.roles.find((r: string) => !r.startsWith('ROLE_')) || user?.sub || '';
  };

  // 获取用户头像
  const getUserAvatar = (): string => {
    const user = uni.getStorageSync(USER_KEY);
    return user?.avatar || '';
  };

  // 清除所有应用缓存数据
  const clearAllCache = () => {
    console.info('🗑️ [AUTH] 清除所有应用缓存数据');
    const deliveryStore = useDeliveryStore();
    const taskStore = useTaskStore();
    const imagesStore = useImagesStore();

    deliveryStore.clearCache();
    taskStore.clearCache();
    imagesStore.clearCache();

    console.info('✅ [AUTH] 所有缓存数据已清除');
  };

  return {
    isAuthenticated,
    getUserInfo,
    getAuth,
    clearAuth,
    checkAuth,
    login,
    logout,
    loadUserData,
    loadUserDataInBackground,
    getUsername,
    getNickname,
    getUserAvatar,
    clearAllCache
  };
});
