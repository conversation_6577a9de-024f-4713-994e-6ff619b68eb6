#!/usr/bin/env node

/**
 * 验证 Iconfont 配置脚本
 *
 * 功能：
 * 1. 检查字体文件是否存在
 * 2. 验证CSS中的Unicode编码是否与iconfont.json匹配
 * 3. 检查Vue文件中使用的图标是否都有定义
 * 4. 生成图标使用报告
 */

const fs = require('fs');
const path = require('path');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');
const FONTS_DIR = path.join(ROOT_DIR, 'static', 'fonts');
const CSS_FILE = path.join(ROOT_DIR, 'static', 'css', 'icons.css');
const ICONFONT_JSON = path.join(FONTS_DIR, 'iconfont.json');

console.log('🔍 验证 Iconfont 配置');
console.log('================================');

// 读取iconfont.json文件
function readIconFontConfig() {
  if (!fs.existsSync(ICONFONT_JSON)) {
    console.log('❌ iconfont.json 文件不存在');
    return null;
  }

  try {
    const content = fs.readFileSync(ICONFONT_JSON, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.log('❌ 读取 iconfont.json 失败:', error.message);
    return null;
  }
}

// 读取CSS文件中的图标定义
function readCSSIcons() {
  if (!fs.existsSync(CSS_FILE)) {
    console.log('❌ icons.css 文件不存在');
    return {};
  }

  const content = fs.readFileSync(CSS_FILE, 'utf8');
  const iconRegex = /\.icon-([^:]+)::before\s*{\s*content:\s*"\\([^"]+)"/g;
  const icons = {};

  let match;
  while ((match = iconRegex.exec(content)) !== null) {
    const iconName = match[1];
    const unicode = match[2];
    icons[iconName] = unicode;
  }

  return icons;
}

// 验证图标映射
function verifyIconMapping() {
  const config = readIconFontConfig();
  if (!config) return false;

  const cssIcons = readCSSIcons();

  console.log('📊 图标映射验证结果：');
  console.log('-'.repeat(60));

  let allCorrect = true;
  const iconMap = {};

  // 建立字体文件中的图标映射
  config.glyphs.forEach(glyph => {
    iconMap[glyph.font_class] = glyph.unicode;
  });

  // 检查CSS中定义的图标
  Object.entries(cssIcons).forEach(([iconName, cssUnicode]) => {
    // 尝试匹配图标名称
    let matchedFontClass = null;
    let expectedUnicode = null;

    // 直接匹配
    if (iconMap[iconName]) {
      matchedFontClass = iconName;
      expectedUnicode = iconMap[iconName];
    } else {
      // 尝试一些常见的映射
      const mappings = {
        'arrow-left': 'angle-left',
        'search': 'search1',
        'building': 'kuangjia',
        'house': 'home',
        'image': 'picture',
        'album': 'xiangce',
        'camera': 'paizhao',
        'upload': 'paper-plane',
        'mobile': 'exchange-fill',
        'users': 'user-group-fill',
        'user': 'user-fill',
        'chart': 'chart-line',
        'calendar': 'calendar-alt',
        'refresh': 'sync-alt',
        'settings': 'setting',
        'location': 'daohang',
        'clipboard': 'CombinedShape',
        'confirm': 'check',
        'cancel': 'times'
      };

      if (mappings[iconName] && iconMap[mappings[iconName]]) {
        matchedFontClass = mappings[iconName];
        expectedUnicode = iconMap[mappings[iconName]];
      }
    }

    if (expectedUnicode) {
      if (cssUnicode === expectedUnicode) {
        console.log(`✅ ${iconName.padEnd(15)} : \\${cssUnicode} (${matchedFontClass})`);
      } else {
        console.log(`❌ ${iconName.padEnd(15)} : \\${cssUnicode} → 应为 \\${expectedUnicode} (${matchedFontClass})`);
        allCorrect = false;
      }
    } else {
      console.log(`⚠️  ${iconName.padEnd(15)} : \\${cssUnicode} (未找到匹配的字体图标)`);
    }
  });

  return allCorrect;
}

// 检查Vue文件中使用的图标
function checkVueIconUsage() {
  console.log('\n📱 Vue文件中的图标使用情况：');
  console.log('-'.repeat(60));

  const cssIcons = readCSSIcons();
  const vueFiles = [
    'pages/delivery/index.vue',
    'pages/delivery/components/delivery-navbar.vue',
    'pages/images/components/images-navbar.vue',
    'pages/my/index.vue',
    'pages/task/index.vue',
    'pages/task/task-detail.vue',
    'pages/task/components/task-navbar.vue',
    'components/custom-tabbar.vue'
  ];

  const usedIcons = new Set();

  vueFiles.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');

      // 查找 icon-xxx 类名
      const iconRegex = /icon-([a-zA-Z-]+)/g;
      let match;
      const fileIcons = new Set();

      while ((match = iconRegex.exec(content)) !== null) {
        const iconName = match[1];
        usedIcons.add(iconName);
        fileIcons.add(iconName);
      }

      if (fileIcons.size > 0) {
        console.log(`📄 ${filePath}:`);
        fileIcons.forEach(icon => {
          const status = cssIcons[icon] ? '✅' : '❌';
          console.log(`   ${status} icon-${icon}`);
        });
      }
    }
  });

  // 检查未使用的图标
  console.log('\n📋 图标使用统计：');
  console.log('-'.repeat(60));

  const definedIcons = Object.keys(cssIcons);
  const unusedIcons = definedIcons.filter(icon => !usedIcons.has(icon));

  console.log(`已定义图标: ${definedIcons.length}`);
  console.log(`已使用图标: ${usedIcons.size}`);
  console.log(`未使用图标: ${unusedIcons.length}`);

  if (unusedIcons.length > 0) {
    console.log('\n未使用的图标:');
    unusedIcons.forEach(icon => {
      console.log(`   icon-${icon}`);
    });
  }
}

// 生成图标预览HTML
function generateIconPreview() {
  const cssIcons = readCSSIcons();
  const config = readIconFontConfig();

  if (!config) return;

  const previewPath = path.join(ROOT_DIR, 'icon-preview.html');

  let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标预览 - ${config.name}</title>
    <link rel="stylesheet" href="static/css/icons.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; }
        .icon-item { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .icon-display { font-size: 32px; margin-bottom: 10px; }
        .icon-name { font-weight: bold; margin-bottom: 5px; }
        .icon-unicode { color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <h1>图标预览 - ${config.name}</h1>
    <p>项目ID: ${config.id} | 字体名称: ${config.font_family}</p>

    <div class="icon-grid">
`;

  Object.entries(cssIcons).forEach(([iconName, unicode]) => {
    html += `
        <div class="icon-item">
            <div class="icon-display">
                <i class="iconfont icon-${iconName}"></i>
            </div>
            <div class="icon-name">icon-${iconName}</div>
            <div class="icon-unicode">\\${unicode}</div>
        </div>
`;
  });

  html += `
    </div>
</body>
</html>
`;

  fs.writeFileSync(previewPath, html);
  console.log(`\n🎨 图标预览文件已生成: ${previewPath}`);
}

// 主函数
function main() {
  // 检查字体文件
  const fontPath = path.join(FONTS_DIR, 'iconfont.ttf');
  if (fs.existsSync(fontPath)) {
    const stats = fs.statSync(fontPath);
    console.log(`✅ 字体文件存在: ${(stats.size / 1024).toFixed(2)} KB`);
  } else {
    console.log('❌ 字体文件不存在: static/fonts/iconfont.ttf');
    return;
  }

  // 验证图标映射
  const mappingCorrect = verifyIconMapping();

  // 检查Vue文件使用情况
  checkVueIconUsage();

  // 生成预览文件
  generateIconPreview();

  console.log('\n' + '='.repeat(60));
  if (mappingCorrect) {
    console.log('🎉 所有图标配置正确！');
  } else {
    console.log('⚠️  发现配置问题，请检查上述错误信息');
  }
}

// 运行脚本
main();
