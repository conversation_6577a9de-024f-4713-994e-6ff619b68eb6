import { showToast } from './toast';

// 声明uni全局对象，解决TypeScript错误
declare const uni: any;

/**
 * API错误响应接口
 */
export interface ErrorResponse {
  /** HTTP状态码 */
  statusCode?: number;
  /** 错误消息 */
  message?: string;
  /** 错误详细数据 */
  data?: any;
  /** 错误代码 */
  code?: string;
}

/**
 * API错误类
 * 用于统一处理API请求中的错误
 */
export class ApiError extends Error {
  /** HTTP状态码 */
  statusCode: number;
  /** 错误详细数据 */
  data?: any;
  /** 错误代码 */
  code?: string;

  /**
   * 创建API错误实例
   * @param message 错误消息
   * @param statusCode HTTP状态码
   * @param data 错误详细数据
   * @param code 错误代码
   */
  constructor(message: string, statusCode: number = 500, data?: any, code?: string) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.data = data;
    this.code = code;

    // 确保instanceof正常工作
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}

/**
 * 错误处理类
 * 提供统一的错误处理机制
 */
export class ErrorHandler {
  /**
   * 处理API错误
   * @param error 错误对象
   * @param options 错误处理选项
   */
  static handleApiError(
    error: any,
    options: {
      showToastMessage?: boolean;
      redirectOnAuthError?: boolean;
      logError?: boolean;
    } = {
      showToastMessage: true,
      redirectOnAuthError: true,
      logError: true
    }
  ): void {
    if (options.logError) {
      console.error('API Error:', error);
    }

    let message = '请求失败，请重试';
    let statusCode = 500;
    let errorCode: string | undefined;

    // 提取错误信息
    if (error instanceof ApiError) {
      message = error.message;
      statusCode = error.statusCode;
      errorCode = error.code;
    } else if (typeof error === 'object' && error !== null) {
      const errorResponse = error as ErrorResponse;
      message = errorResponse.message || message;
      statusCode = errorResponse.statusCode || statusCode;
      errorCode = errorResponse.code;
    }

    // 根据状态码处理不同类型的错误
    switch (statusCode) {
      case 401:
        message = '未登录或登录已过期';
        if (options.redirectOnAuthError) {
          uni.reLaunch({ url: '/pages/login/login' });
        }
        break;
      case 403:
        message = '没有权限访问';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 429:
        message = '请求过于频繁，请稍后再试';
        break;
      case 500:
        message = '服务器内部错误';
        break;
      case 502:
        message = '网关错误';
        break;
      case 503:
        message = '服务不可用';
        break;
      case 504:
        message = '网关超时';
        break;
    }

    // 显示错误提示
    if (options.showToastMessage) {
      showToast(message, 'none');
    }
  }

  /**
   * 包装API调用，统一处理错误
   * @param apiCall API调用函数
   * @param errorOptions 错误处理选项
   * @returns API调用结果
   */
  static async wrapApiCall<T>(
    apiCall: () => Promise<T>,
    errorOptions?: {
      showToastMessage?: boolean;
      redirectOnAuthError?: boolean;
      logError?: boolean;
      rethrowError?: boolean;
    }
  ): Promise<T | null> {
    const options = {
      showToastMessage: true,
      redirectOnAuthError: true,
      logError: true,
      rethrowError: true,
      ...errorOptions
    };

    try {
      return await apiCall();
    } catch (error) {
      this.handleApiError(error, options);

      if (options.rethrowError) {
        throw error;
      }

      return null;
    }
  }
}