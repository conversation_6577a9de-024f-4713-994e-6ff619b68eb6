import { showToast } from '@/utils';

/**
 * 智能数据加载器
 * 负责控制数据加载时机和策略
 */
export class SmartDataLoader {
	private readonly CACHE_KEY = 'daily_data_cache';
	private readonly LOAD_TIME_KEY = 'last_load_time';
	private readonly USER_KEY = 'last_load_user';

	private isLoading = false;
	private loadingPromise: Promise<void> | null = null;

	/**
	 * 检查是否需要加载数据
	 */
	shouldLoadData(currentUser: string): boolean {
		try {
			// 1. 检查用户是否变更
			const lastUser = uni.getStorageSync(this.USER_KEY);
			if (lastUser !== currentUser) {
				console.info('🔄 [LOADER] 用户变更，需要重新加载数据');
				return true;
			}

			// 2. 检查是否今日未加载
			const lastLoadTime = this.getLastLoadTime();
			if (!lastLoadTime) {
				console.info('🔄 [LOADER] 从未加载过数据，需要加载');
				return true;
			}

			// 3. 检查是否跨日（以0点为界）
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			const lastLoadDate = new Date(lastLoadTime);
			lastLoadDate.setHours(0, 0, 0, 0);

			if (lastLoadDate < today) {
				console.info('🔄 [LOADER] 跨日检测，需要重新加载数据');
				return true;
			}

			console.info('✅ [LOADER] 今日已加载，使用缓存数据');
			return false;
		} catch (error) {
			console.error('❌ [LOADER] 检查加载条件失败:', error);
			return true; // 出错时强制加载
		}
	}

	/**
	 * 强制加载数据
	 */
	async forceLoadData(
		currentUser: string,
		loadDeliveries: () => Promise<void>,
		loadTasks: () => Promise<void>,
		onProgress?: (stage: string, progress: number) => void
	): Promise<void> {
		// 防止重复加载
		if (this.isLoading) {
			console.info('⏳ [LOADER] 数据正在加载中，等待完成');
			return this.loadingPromise || Promise.resolve();
		}

		this.isLoading = true;
		console.info('🚀 [LOADER] 开始强制加载数据');

		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		// 显示加载指示器
		showToast('正在加载今日数据...', 'loading');

		this.loadingPromise = this.executeLoad(
			currentUser,
			loadDeliveries,
			loadTasks,
			onProgress
		);

		try {
			await this.loadingPromise;

			const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
			const loadTime = endTime - startTime;

			// 记录加载时间和用户
			this.recordLoadTime(currentUser);

			console.info(`✅ [LOADER] 数据加载完成，耗时: ${loadTime.toFixed(2)}ms`);
			showToast('数据加载完成', 'success', 1000);

		} catch (error) {
			console.error('❌ [LOADER] 数据加载失败:', error);
			showToast('数据加载失败，请重试', 'error');
			throw error;
		} finally {
			this.isLoading = false;
			this.loadingPromise = null;
			uni.hideLoading();
		}
	}

	/**
	 * 执行数据加载
	 */
	private async executeLoad(
		currentUser: string,
		loadDeliveries: () => Promise<void>,
		loadTasks: () => Promise<void>,
		onProgress?: (stage: string, progress: number) => void
	): Promise<void> {
		try {
			// 阶段1：加载派单数据
			onProgress?.('正在加载派单数据...', 25);
			await loadDeliveries();
			console.info('📋 [LOADER] 派单数据加载完成');

			// 阶段2：加载任务数据
			onProgress?.('正在加载任务数据...', 50);
			await loadTasks();
			console.info('📝 [LOADER] 任务数据加载完成');

			// 阶段3：数据分析（将在 DataAnalysisEngine 中处理）
			onProgress?.('正在分析数据...', 75);

			// 阶段4：完成
			onProgress?.('加载完成', 100);

		} catch (error) {
			console.error('❌ [LOADER] 执行加载失败:', error);
			throw error;
		}
	}

	/**
	 * 检查是否正在加载
	 */
	isLoadingData(): boolean {
		return this.isLoading;
	}

	/**
	 * 获取最后加载时间
	 */
	getLastLoadTime(): number | null {
		try {
			const timestamp = uni.getStorageSync(this.LOAD_TIME_KEY);
			return timestamp ? parseInt(timestamp) : null;
		} catch (error) {
			console.warn('⚠️ [LOADER] 获取最后加载时间失败:', error);
			return null;
		}
	}

	/**
	 * 获取最后加载用户
	 */
	getLastLoadUser(): string | null {
		try {
			return uni.getStorageSync(this.USER_KEY) || null;
		} catch (error) {
			console.warn('⚠️ [LOADER] 获取最后加载用户失败:', error);
			return null;
		}
	}

	/**
	 * 记录加载时间和用户
	 */
	private recordLoadTime(currentUser: string): void {
		try {
			const now = Date.now();
			uni.setStorageSync(this.LOAD_TIME_KEY, now.toString());
			uni.setStorageSync(this.USER_KEY, currentUser);
			console.info(`📝 [LOADER] 记录加载时间: ${new Date(now).toLocaleString()}, 用户: ${currentUser}`);
		} catch (error) {
			console.warn('⚠️ [LOADER] 记录加载时间失败:', error);
		}
	}

	/**
	 * 清除加载记录（用于测试或重置）
	 */
	clearLoadRecord(): void {
		try {
			uni.removeStorageSync(this.LOAD_TIME_KEY);
			uni.removeStorageSync(this.USER_KEY);
			console.info('🗑️ [LOADER] 已清除加载记录');
		} catch (error) {
			console.warn('⚠️ [LOADER] 清除加载记录失败:', error);
		}
	}

	/**
	 * 获取加载状态信息
	 */
	getLoadStatus(): {
		isLoading: boolean;
		lastLoadTime: number | null;
		lastLoadUser: string | null;
		shouldLoad: (user: string) => boolean;
	} {
		return {
			isLoading: this.isLoading,
			lastLoadTime: this.getLastLoadTime(),
			lastLoadUser: this.getLastLoadUser(),
			shouldLoad: (user: string) => this.shouldLoadData(user)
		};
	}
}

// 创建全局实例
export const smartDataLoader = new SmartDataLoader();
