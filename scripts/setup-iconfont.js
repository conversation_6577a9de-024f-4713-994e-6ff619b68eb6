#!/usr/bin/env node

/**
 * 自动配置 Iconfont 脚本
 * 
 * 使用方法：
 * 1. 手动从 iconfont.cn 下载字体文件到 static/fonts/iconfont.ttf
 * 2. 运行此脚本验证配置：node scripts/setup-iconfont.js
 */

const fs = require('fs');
const path = require('path');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');
const FONTS_DIR = path.join(ROOT_DIR, 'static', 'fonts');
const CSS_FILE = path.join(ROOT_DIR, 'static', 'css', 'icons.css');

// 需要的图标列表
const REQUIRED_ICONS = [
  'icon-wifi',          // WiFi信号
  'icon-navigation',      // 位置定位
  'icon-search',        // 搜索
  'icon-arrow-left',    // 左箭头
  'icon-close',         // 关闭
  'icon-menu',          // 菜单
  'icon-house',         // 房屋
  'icon-building',      // 建筑
  'icon-clipboard',     // 剪贴板
  'icon-camera',        // 相机
  'icon-upload',        // 上传
  'icon-check',         // 勾选
  'icon-settings',      // 设置
  'icon-refresh',       // 刷新
  'icon-image',         // 图片
  'icon-clock',         // 时钟
  'icon-mobile',        // 手机
  'icon-calendar',      // 日历
  'icon-chart',         // 图表
  'icon-users',         // 用户组
  'icon-user',          // 用户
  'icon-confirm',       // 确认
  'icon-cancel'         // 取消
];

// 推荐的iconfont图标搜索关键词
const ICON_KEYWORDS = {
  'icon-wifi': 'wifi 信号',
  'icon-navigation': '位置 定位 地图',
  'icon-search': '搜索 查找',
  'icon-arrow-left': '箭头 返回 左',
  'icon-close': '关闭 删除 叉',
  'icon-menu': '菜单 更多 三横线',
  'icon-house': '房屋 家 住宅',
  'icon-building': '建筑 大楼 办公',
  'icon-clipboard': '剪贴板 文档 列表',
  'icon-camera': '相机 拍照',
  'icon-upload': '上传 云',
  'icon-check': '勾选 对号 完成',
  'icon-settings': '设置 齿轮',
  'icon-refresh': '刷新 重新加载',
  'icon-image': '图片 照片',
  'icon-clock': '时钟 时间',
  'icon-mobile': '手机 移动',
  'icon-calendar': '日历 日期',
  'icon-chart': '图表 统计',
  'icon-users': '用户组 团队',
  'icon-user': '用户 个人',
  'icon-confirm': '确认 对号',
  'icon-cancel': '取消 叉号'
};

console.log('🚀 Iconfont 配置检查工具');
console.log('================================');

// 检查字体文件
function checkFontFile() {
  const fontPath = path.join(FONTS_DIR, 'iconfont.ttf');
  
  if (!fs.existsSync(fontPath)) {
    console.log('❌ 字体文件不存在: static/fonts/iconfont.ttf');
    console.log('');
    console.log('📥 请按以下步骤下载字体文件：');
    console.log('1. 访问 https://www.iconfont.cn/');
    console.log('2. 注册并登录账号');
    console.log('3. 搜索以下图标并添加到项目：');
    console.log('');
    
    REQUIRED_ICONS.forEach(icon => {
      const keywords = ICON_KEYWORDS[icon] || icon.replace('icon-', '');
      console.log(`   ${icon}: ${keywords}`);
    });
    
    console.log('');
    console.log('4. 创建项目并下载字体文件');
    console.log('5. 将 iconfont.ttf 文件放置到 static/fonts/ 目录');
    console.log('6. 重新运行此脚本');
    
    return false;
  }
  
  const stats = fs.statSync(fontPath);
  console.log(`✅ 字体文件存在: ${fontPath}`);
  console.log(`   文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
  return true;
}

// 检查CSS配置
function checkCSSConfig() {
  if (!fs.existsSync(CSS_FILE)) {
    console.log('❌ CSS文件不存在: static/css/icons.css');
    return false;
  }
  
  const cssContent = fs.readFileSync(CSS_FILE, 'utf8');
  const missingIcons = [];
  
  REQUIRED_ICONS.forEach(icon => {
    if (!cssContent.includes(`.${icon}::before`)) {
      missingIcons.push(icon);
    }
  });
  
  if (missingIcons.length > 0) {
    console.log('⚠️  CSS配置中缺少以下图标定义:');
    missingIcons.forEach(icon => {
      console.log(`   ${icon}`);
    });
    return false;
  }
  
  console.log('✅ CSS配置完整');
  return true;
}

// 生成iconfont项目配置
function generateIconFontConfig() {
  console.log('');
  console.log('📋 Iconfont 项目配置建议：');
  console.log('================================');
  console.log('项目名称: 任务管理图标');
  console.log('字体名称: iconfont');
  console.log('类名前缀: icon-');
  console.log('');
  console.log('需要搜索的图标关键词：');
  
  Object.entries(ICON_KEYWORDS).forEach(([icon, keywords]) => {
    console.log(`${icon.padEnd(20)} : ${keywords}`);
  });
}

// 主函数
function main() {
  const fontExists = checkFontFile();
  const cssValid = checkCSSConfig();
  
  if (fontExists && cssValid) {
    console.log('');
    console.log('🎉 Iconfont 配置完成！');
    console.log('所有图标都已正确配置，可以正常使用。');
  } else {
    generateIconFontConfig();
  }
  
  console.log('');
  console.log('💡 提示：');
  console.log('- 如需更新图标，请重新下载字体文件并更新CSS');
  console.log('- 确保字体文件路径正确: static/fonts/iconfont.ttf');
  console.log('- 图标Unicode编码需要与下载的字体文件匹配');
}

// 运行脚本
main();
