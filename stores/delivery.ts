import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Delivery } from '@/types';
import { showToast, formatDate } from '@/utils';
import { DeliveryApi } from '@/utils/api';
import { useAuthStore } from './auth';
import { useSettingsStore } from './settings';
import { getDateRange } from '@/utils/date';

export const useDeliveryStore = defineStore('delivery', () => {
	// 派单列表
	const deliveries = ref<Delivery[]>([]);
	// 加载状态
	const loading = ref(false);
	// 刷新状态
	const refreshing = ref(false);
	// 是否已有数据
	const hasData = ref(false);
	// 本地缓存key
	const CACHE_KEY = 'delivery_cache';
	const SELECTED_IDS_KEY = 'delivery_selected_ids';
	const MANUAL_MERGED_KEY = 'delivery_manual_merged';
	const FILTER_SETTINGS_KEY = 'delivery_filter_settings';

	// 选中的派单ID列表
	const selectedDeliveryIds = ref<string[]>([]);
	// 手动合并后的派单
	const manualMergedDelivery = ref<any>(null);
	// 筛选设置
	const filterSettings = ref({
		mergeType: 'no_merge', // 合并类型：no_merge, merge_today, manual_merge
		sortOrder: 'time_asc', // 排序方式：time_asc, time_desc
	});

	// 从缓存加载数据
	const loadFromCache = () => {
		try {
			const cached = uni.getStorageSync(CACHE_KEY);
			if (cached) {
				deliveries.value = JSON.parse(cached);
				hasData.value = true;
				return true;
			}
		} catch (error) {
			console.warn('读取缓存失败:', error);
		}
		return false;
	};

	// 保存数据到缓存
	const saveToCache = () => {
		try {
			uni.setStorageSync(CACHE_KEY, JSON.stringify(deliveries.value));
		} catch (error) {
			console.warn('保存缓存失败:', error);
		}
	};

	// 加载派单任务
	const loadDeliveries = async (forceLoad = false) => {
		// 如果已有数据且不是强制加载，尝试从缓存加载
		if (hasData.value && !forceLoad) {
			if (loadFromCache()) return;
		}

		if (!refreshing.value) {
			loading.value = true;
		}

		try {
			const authStore = useAuthStore();
			const settingsStore = useSettingsStore();
			const username = authStore.getUsername();

			// 验证用户名是否有效
			if (!username || username.trim() === '') {
				console.error('❌ [DELIVERY] 用户名为空，无法加载派单');
				showToast('用户信息无效，请重新登录', 'none');
				return;
			}

			// 根据设置获取时间范围
			const { startDay, endDay } = getDateRange(settingsStore.settings.orderPeriod);

			console.info(`📋 [DELIVERY] 开始加载派单: 用户=${username}, 时间范围=${startDay} ~ ${endDay}`);

			const response = await DeliveryApi.loadUserDeliveries(
				username,
				startDay,
				endDay
			);

			deliveries.value = response?.data || [];
			hasData.value = true;
			saveToCache();
			console.info('📋加载了「' + deliveries.value.length + '」条派单');
			// showToast('加载了「' + deliveries.value.length + '」条派单', 'none', 500);
		} catch (error) {
			console.error('❌ [DELIVERY] 加载派单失败:', error);
			// showToast('加载派单失败，请稍后重试', 'none');
		} finally {
			loading.value = false;
		}
	};

	// 刷新派单任务
	const refreshDeliveries = async () => {
		try {
			refreshing.value = true;
			await loadDeliveries(true);
			// 不需要在这里调用 afterLoadDeliveries，因为 loadDeliveries 已经被包装
		} finally {
			refreshing.value = false;
		}
	};

	// 获取派单对应的派单名称
	const getDeliveryName = (delivery: Delivery) => {
		const nameParts = [
			delivery?.deliveryName || '',
			delivery?.photoRequirements ? `(${delivery.photoRequirements})` : '',
		].filter((part) => part && part.trim());
		return nameParts.join(' ') || '未设置';
	};

	// 从本地存储加载状态
	const loadStateFromStorage = () => {
		try {
			// 加载选中的派单ID列表
			const savedSelectedIds = uni.getStorageSync(SELECTED_IDS_KEY);
			if (savedSelectedIds) {
				selectedDeliveryIds.value = JSON.parse(savedSelectedIds);
			}

			// 加载手动合并后的派单
			const savedManualMerged = uni.getStorageSync(MANUAL_MERGED_KEY);
			if (savedManualMerged) {
				manualMergedDelivery.value = JSON.parse(savedManualMerged);
			}

			// 加载筛选设置
			const savedFilterSettings = uni.getStorageSync(FILTER_SETTINGS_KEY);
			if (savedFilterSettings) {
				filterSettings.value = JSON.parse(savedFilterSettings);
			}
		} catch (error) {
			console.warn('加载状态失败:', error);
		}
	};

	// 保存状态到本地存储
	const saveStateToStorage = () => {
		try {
			// 保存选中的派单ID列表
			uni.setStorageSync(SELECTED_IDS_KEY, JSON.stringify(selectedDeliveryIds.value));

			// 保存手动合并后的派单
			uni.setStorageSync(MANUAL_MERGED_KEY, JSON.stringify(manualMergedDelivery.value));

			// 保存筛选设置
			uni.setStorageSync(FILTER_SETTINGS_KEY, JSON.stringify(filterSettings.value));
		} catch (error) {
			console.warn('保存状态失败:', error);
		}
	};

	// 设置选中的派单ID列表
	const setSelectedDeliveryIds = (ids: string[]) => {
		selectedDeliveryIds.value = ids;
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取选中的派单ID列表
	const getSelectedDeliveryIds = () => selectedDeliveryIds.value;

	// 设置手动合并后的派单
	const setManualMergedDelivery = (delivery: any) => {
		manualMergedDelivery.value = delivery;
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取手动合并后的派单
	const getManualMergedDelivery = () => manualMergedDelivery.value;

	// 设置筛选设置
	const setFilterSettings = (settings: any) => {
		filterSettings.value = { ...filterSettings.value, ...settings };
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取筛选设置
	const getFilterSettings = () => filterSettings.value;

	// 合并当天任务的辅助函数
	const mergeTodayDeliveries = (deliveryList: Delivery[]) => {
		// 按日期分组，同一天的任务合并显示
		const groupedByDate: Record<string, any> = {};

		deliveryList.forEach(item => {
			// 获取日期部分（不含时间）
			const dateStr = item.deliveryDate ? new Date(item.deliveryDate).toISOString().split('T')[0] : 'unknown';

			if (!groupedByDate[dateStr]) {
				// 创建新组
				groupedByDate[dateStr] = {
					...item,
					isGrouped: true,
					groupItems: [item],
					deliveryName: getDeliveryName(item), // 只使用第一个派单的名称
					mergedCount: 1, // 初始化合并数量
				};
			} else {
				// 添加到现有组
				const group = groupedByDate[dateStr];
				group.groupItems.push(item);
				group.mergedCount = group.groupItems.length; // 更新合并数量
			}
		});

		// 转换回数组
		return Object.values(groupedByDate);
	};

	// 根据筛选条件过滤和排序派单列表
	const getFilteredDeliveries = (searchText = '', taskStore?: any) => {
		// 首先应用搜索过滤
		let filtered = deliveries.value;

		// 应用搜索文本过滤
		if (searchText) {
			const searchLower = searchText.toLowerCase();
			filtered = filtered.filter((item: any) => {
				// 搜索派单名称
				if (getDeliveryName(item).toLowerCase().includes(searchLower)) return true;
				// 搜索派单ID
				if (item.deliveryId && item.deliveryId.toString().toLowerCase().includes(searchLower)) return true;
				// 搜索队列ID
				if (item.queueId && item.queueId.toString().toLowerCase().includes(searchLower)) return true;
				return false;
			});
		}

		// 应用完成状态过滤
		const settingsStore = useSettingsStore();
		const completionStatus = settingsStore.settings.deliveryDisplaySettings?.completionStatus || 'incomplete';

		if (completionStatus !== 'all' && taskStore) {
			filtered = filtered.filter((item: any) => {
				// 使用 getDeliveryState 获取准确的任务统计
				const stats = taskStore.getDeliveryState(item.deliveryId);

				// 根据任务统计判断完成状态
				// 完成条件：已传 + 待传 >= 应拍
				const isComplete = stats.requiredCount > 0 &&
					(stats.uploadedCount + stats.pendingCount) >= stats.requiredCount;

				if (completionStatus === 'complete') {
					return isComplete; // 仅显示已完成
				} else if (completionStatus === 'incomplete') {
					return !isComplete; // 仅显示未完成
				}
				return true;
			});
		}

		// 应用排序
		const sorted = [...filtered].sort((a, b) => {
			const dateA = new Date(a.deliveryDate).getTime();
			const dateB = new Date(b.deliveryDate).getTime();
			return filterSettings.value.sortOrder === 'time_asc' ? dateA - dateB : dateB - dateA;
		});

		// 如果选择了手动合并，并且有手动合并的结果，则只显示手动合并的结果
		if (filterSettings.value.mergeType === 'manual_merge' && manualMergedDelivery.value) {
			return [manualMergedDelivery.value];
		}

		// 根据合并类型进行处理
		if (filterSettings.value.mergeType === 'no_merge') {
			// 不合并，直接返回排序后的列表
			return sorted;
		} else if (filterSettings.value.mergeType === 'merge_today') {
			// 合并当天任务
			return mergeTodayDeliveries(sorted);
		} else {
			// 默认返回排序后的列表
			return sorted;
		}
	};

	// 在loadDeliveries后重新计算手动合并的派单
	const recalculateManualMergedDelivery = () => {
		// 如果没有手动合并的派单，直接返回
		if (!manualMergedDelivery.value) return;

		// 获取手动合并的派单中的所有派单ID
		const mergedIds = manualMergedDelivery.value.groupItems.map((item: any) => item.deliveryId);

		// 检查这些ID是否仍然存在于当前的派单列表中
		const existingDeliveries = deliveries.value.filter((item: any) =>
			mergedIds.includes(item.deliveryId)
		);

		// 如果没有找到任何派单，清空手动合并的派单
		if (existingDeliveries.length === 0) {
			manualMergedDelivery.value = null;
			return;
		}

		// 如果找到的派单数量与原来不同，需要重新创建手动合并的派单
		if (existingDeliveries.length !== mergedIds.length) {
			// 创建新的手动合并派单
			const firstDelivery = existingDeliveries[0];
			manualMergedDelivery.value = {
				...firstDelivery,
				deliveryId: `merged_${Date.now()}`, // 生成唯一ID
				isGrouped: true,
				groupItems: existingDeliveries,
				deliveryName: getDeliveryName(firstDelivery),
				mergedCount: existingDeliveries.length,
				deliveryDate: firstDelivery.deliveryDate,
				currentContentImageUrl: firstDelivery.currentContentImageUrl
			};
		}
	};

	// 在loadDeliveries后调用
	const afterLoadDeliveries = () => {
		// 重新计算手动合并的派单
		recalculateManualMergedDelivery();
	};

	// 包装loadDeliveries，在加载完成后调用afterLoadDeliveries
	const wrappedLoadDeliveries = async (forceLoad = false) => {
		// 先从本地存储加载状态
		loadStateFromStorage();

		// 加载派单数据
		await loadDeliveries(forceLoad);

		// 重新计算手动合并的派单
		afterLoadDeliveries();
	};

	return {
		deliveries,
		loading,
		refreshing,
		hasData,
		selectedDeliveryIds,
		manualMergedDelivery,
		filterSettings,
		getDeliveryName,
		loadDeliveries: wrappedLoadDeliveries, // 使用包装后的函数
		refreshDeliveries,
		setSelectedDeliveryIds,
		getSelectedDeliveryIds,
		setManualMergedDelivery,
		getManualMergedDelivery,
		setFilterSettings,
		getFilterSettings,
		getFilteredDeliveries,
		mergeTodayDeliveries,
		loadStateFromStorage, // 暴露加载状态方法
		saveStateToStorage, // 暴露保存状态方法
	};
});
