<template>
	<view class="performance-test">
		<view class="header">
			<text class="title">性能测试页面</text>
		</view>
		
		<view class="controls">
			<button @click="toggleMode" :class="{ active: useOptimizedMode }">
				{{ useOptimizedMode ? '优化模式' : '兼容模式' }}
			</button>
			<button @click="loadTestData">加载测试数据</button>
			<button @click="clearData">清除数据</button>
		</view>
		
		<view class="stats">
			<view class="stat-item">
				<text class="label">数据模式:</text>
				<text class="value">{{ useOptimizedMode ? '优化模式' : '兼容模式' }}</text>
			</view>
			<view class="stat-item">
				<text class="label">任务总数:</text>
				<text class="value">{{ taskStore.tasks.length }}</text>
			</view>
			<view class="stat-item">
				<text class="label">核心数据:</text>
				<text class="value">{{ taskCores.length }}</text>
			</view>
			<view class="stat-item">
				<text class="label">渲染耗时:</text>
				<text class="value">{{ lastRenderTime }}ms</text>
			</view>
			<view class="stat-item">
				<text class="label">内存使用:</text>
				<text class="value">{{ memoryUsage }}MB</text>
			</view>
		</view>
		
		<view class="task-list">
			<view class="list-header">
				<text>任务列表 (显示前50个)</text>
			</view>
			<scroll-view scroll-y class="scroll-container">
				<view v-for="task in displayTasks" :key="task.taskId || task.id" class="task-item">
					<view class="task-info">
						<text class="task-id">{{ task.taskId || task.id }}</text>
						<text class="task-name">{{ task.code || task.spotCode || task.zoneName }}</text>
						<text class="task-type">{{ task.type || (task.spotId ? '点位' : '实景') }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { useTaskStore } from '@/stores/task';
import { useOptimizedTaskList } from '@/composables/useOptimizedTaskList';
import { storeToRefs } from 'pinia';

// 性能测试开关
const useOptimizedMode = ref(true);

// Store 和优化层
const taskStore = useTaskStore();
const { tasks } = storeToRefs(taskStore);
const optimizedTaskList = useOptimizedTaskList();
const { taskCores } = optimizedTaskList;

// 性能监控
const lastRenderTime = ref(0);
const memoryUsage = ref(0);

// 显示的任务列表
const displayTasks = computed(() => {
	const startTime = performance.now();
	
	let result;
	if (useOptimizedMode.value && taskCores.value.length > 0) {
		// 优化模式：使用轻量级核心数据
		result = taskCores.value.slice(0, 50);
	} else {
		// 兼容模式：使用完整任务数据
		result = tasks.value.slice(0, 50);
	}
	
	nextTick(() => {
		const endTime = performance.now();
		lastRenderTime.value = Number((endTime - startTime).toFixed(2));
		updateMemoryUsage();
	});
	
	return result;
});

// 切换模式
const toggleMode = () => {
	useOptimizedMode.value = !useOptimizedMode.value;
	console.info(`🔄 [PERFORMANCE TEST] 切换到${useOptimizedMode.value ? '优化' : '兼容'}模式`);
};

// 加载测试数据
const loadTestData = async () => {
	console.info('📊 [PERFORMANCE TEST] 开始加载测试数据');
	const startTime = performance.now();
	
	try {
		await taskStore.loadTasks(true);
		const endTime = performance.now();
		console.info(`📊 [PERFORMANCE TEST] 数据加载完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
	} catch (error) {
		console.error('❌ [PERFORMANCE TEST] 数据加载失败:', error);
	}
};

// 清除数据
const clearData = () => {
	taskStore.tasks.splice(0);
	console.info('🗑️ [PERFORMANCE TEST] 数据已清除');
};

// 更新内存使用情况
const updateMemoryUsage = () => {
	if (performance.memory) {
		const used = performance.memory.usedJSHeapSize;
		memoryUsage.value = Number((used / 1024 / 1024).toFixed(2));
	}
};

// 监控数据变化
watch([tasks, taskCores], () => {
	console.info(`📊 [PERFORMANCE TEST] 数据变化 - 完整任务: ${tasks.value.length}, 核心数据: ${taskCores.value.length}`);
}, { immediate: true });

onMounted(() => {
	updateMemoryUsage();
	console.info('🚀 [PERFORMANCE TEST] 性能测试页面已加载');
});
</script>

<style lang="scss" scoped>
.performance-test {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.controls {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
	
	button {
		flex: 1;
		padding: 20rpx;
		border-radius: 10rpx;
		border: none;
		background-color: #007aff;
		color: white;
		font-size: 28rpx;
		
		&.active {
			background-color: #28a745;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.stats {
	background-color: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	
	.stat-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx 0;
		border-bottom: 1rpx solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
		
		.label {
			font-size: 28rpx;
			color: #666;
		}
		
		.value {
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
		}
	}
}

.task-list {
	background-color: white;
	border-radius: 10rpx;
	overflow: hidden;
	
	.list-header {
		padding: 30rpx;
		background-color: #f8f9fa;
		border-bottom: 1rpx solid #eee;
		
		text {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.scroll-container {
		height: 600rpx;
	}
	
	.task-item {
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
		
		.task-info {
			display: flex;
			gap: 20rpx;
			align-items: center;
			
			.task-id {
				font-size: 24rpx;
				color: #999;
				min-width: 120rpx;
			}
			
			.task-name {
				font-size: 28rpx;
				color: #333;
				flex: 1;
			}
			
			.task-type {
				font-size: 24rpx;
				color: #007aff;
				padding: 8rpx 16rpx;
				background-color: #e3f2fd;
				border-radius: 20rpx;
			}
		}
	}
}
</style>
