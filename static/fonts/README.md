# 字体文件目录

## 📁 目录说明

这个目录用于存放 Iconfont 字体文件。

## 📥 需要放置的文件

请将从 Iconfont 下载的字体文件放置在此目录：

```
static/fonts/
└── iconfont.ttf  ← 从 iconfont.cn 下载的字体文件
```

## 🚀 获取字体文件步骤

1. **访问 Iconfont 网站**
   - 网址：https://www.iconfont.cn/

2. **搜索并收集图标**
   - wifi - WiFi 图标
   - location - 位置图标  
   - search - 搜索图标
   - arrow-left - 返回箭头
   - close - 关闭图标
   - menu - 菜单图标
   - house - 房屋图标
   - building - 建筑图标
   - clipboard - 剪贴板图标

3. **创建项目并下载**
   - 添加图标到购物车
   - 创建新项目
   - 下载至本地
   - 将 `iconfont.ttf` 文件复制到此目录

4. **更新图标编码**
   - 打开下载包中的 `iconfont.css`
   - 复制 Unicode 编码到 `static/css/icons.css`

## ⚠️ 注意事项

- 确保字体文件名为 `iconfont.ttf`
- 文件大小通常在 10-50KB 之间
- 支持的格式：TTF (TrueType Font)

## 🔧 故障排除

如果图标不显示：
1. 检查字体文件是否存在
2. 检查文件名是否正确
3. 检查 CSS 中的路径是否正确
4. 清除浏览器缓存重新加载
