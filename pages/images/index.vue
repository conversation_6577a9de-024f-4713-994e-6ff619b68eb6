<template>
	<view class="images-container" :style="dynamicFontStyle">
		<!-- 图片上传导航栏 -->
		<images-navbar
			:total-count="localImages.length"
			:pending-count="uploadingCount"
			:is-uploading="uploadStatus.isUploading"
			:is-paused="uploadStatus.isPaused"
			:is-wifi="uploadStatus.isWifi"
			:network-type="uploadStatus.networkType"
			:current="uploadStatus.current"
			:total="uploadStatus.total"
			@dropdown="toggleDropdown"
			@search="onSearchClick"
			@menu="onMenuClick"
			@upload="handleUploadToggle" />

		<!-- 上传进度显示区域 -->
		<view v-if="uploadStatus.isUploading && !uploadStatus.isPaused && uploadStatus.total > 0"
			class="upload-progress-bar"
			:style="{ top: 'calc(44px + var(--status-bar-height))' }">
			<view class="progress-content">
				<view class="progress-info">
					<text class="progress-text">正在上传第 {{ uploadStatus.current + 1 }} 张，共 {{ uploadStatus.total }} 张</text>
					<text class="progress-percentage">{{ Math.round(((uploadStatus.current + 1) / uploadStatus.total) * 100) }}%</text>
				</view>
				<view class="progress-track">
					<view class="progress-fill" :style="{ width: ((uploadStatus.current + 1) / uploadStatus.total) * 100 + '%' }"></view>
				</view>
			</view>
		</view>

		<scroll-view scroll-y refresher-enabled :refresher-triggered="loading" @refresherrefresh="loadLocalImages"
			class="images-list"
			:style="{
				marginTop: uploadStatus.isUploading && !uploadStatus.isPaused && uploadStatus.total > 0
					? 'calc(44px + var(--status-bar-height) + 60px)'
					: 'calc(44px + var(--status-bar-height))'
			}">
			<scroll-view scroll-y class="content">
				<view v-if="taskLocalPendingmages.length === 0" class="empty-state">
					<view class="empty-icon">📷</view>
					<text class="empty-text">{{ searchText ? '没有符合搜索条件的图片' : '暂无待上传图片' }}</text>
				</view>
				<view v-else class="image-list">
					<view v-for="image in taskLocalPendingmages" :key="image.imageUrl" class="image-item" @click="handleImageClick(image)">
						<view class="image-preview">
							<image :src="image.imageUrl" mode="aspectFill" class="preview-image"></image>
							<view class="image-status" :class="image.imageStatus">
								{{ image.imageStatus !== 'pending' ? '已上传' : '待上传' }}
							</view>
						</view>
						<view class="image-content">
							<view class="image-info">
								<view class="info-header">
									<text class="info-title">
										{{ getTaskInfo(image.taskId)?.zoneTask ? '实景位置' : '点位编号' }}：
										{{ getTaskInfo(image.taskId)?.zoneTask ? '实景拍摄' : (getTaskInfo(image.taskId)?.spotCode || '未设置') }}
									</text>
									<!-- 如果是折叠任务，显示折叠标记 -->
									<text v-if="getTaskInfo(image.taskId)?.isCollapsed" class="collapsed-badge">
										(折叠任务)
									</text>
									<!-- 如果有折叠的任务，显示折叠任务数量 -->
									<text v-if="getTaskInfo(image.taskId)?.collapsedZoneTasks && getTaskInfo(image.taskId)?.collapsedZoneTasks.length > 0" class="merged-count">
										({{ getTaskInfo(image.taskId)?.collapsedZoneTasks.length + 1 }} 个任务)
									</text>
								</view>
								<view class="info-row">
									<text class="label">小区：</text>
									<text class="value">{{ getTaskInfo(image.taskId)?.zoneName || '未设置' }}</text>
								</view>
								<view class="info-row">
									<text class="label">地址：</text>
									<text class="value">{{ getTaskInfo(image.taskId)?.zoneAddress || '未设置' }}</text>
								</view>
								<view class="info-row">
									<text class="label">位置：</text>
									<text
										class="value">{{ getTaskInfo(image.taskId) ? getShotLocation(getTaskInfo(image.taskId)) : '未设置' }}</text>
								</view>
								<view class="info-row delivery-row">
									<text class="label">派单：</text>
									<view class="value-container">
										<!-- 如果是数组，分行显示每个派单 -->
										<template v-if="Array.isArray(getTaskDeliveryNameById(image.taskId))">
											<view v-for="(deliveryName, index) in getTaskDeliveryNameById(image.taskId)" :key="index" class="delivery-item">
												{{ deliveryName }}
											</view>
										</template>
										<!-- 如果不是数组，直接显示 -->
										<text v-else class="value">
											{{ getTaskDeliveryNameById(image.taskId) }}
										</text>
									</view>
								</view>
								<view class="info-row">
									<text class="label">拍照：</text>
									<text class="value">{{ formatDateTime(image.shotTime) }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</scroll-view>
	</view>
</template>

<script setup lang="ts">
	import { computed, onMounted, onUnmounted, ref } from 'vue';
	import { onShow } from '@dcloudio/uni-app';
	import { storeToRefs } from 'pinia';
	import { useImagesStore, useTaskStore, useAuthStore } from '@/stores';
	import { formatDateTime, showToast } from '@/utils';

// 声明uni全局对象，解决TypeScript错误
declare const uni: any;

	const taskStore = useTaskStore();
	const imagesStore = useImagesStore();

	const { localImages, loading } = storeToRefs(imagesStore);
	const { tasks } = storeToRefs(taskStore);

	const {
		uploadTaskImage,
		deleteLocalImage,
		loadLocalImages,
		startUpload,
		pauseUpload,
		resumeUpload,
		getUploadStatus
	} = imagesStore;
	const { getTaskDeliveryNameById } = taskStore;

	// 上传状态
	const uploadStatus = ref({
		isUploading: false,
		isPaused: false,
		total: 0,
		current: 0,
		networkType: 'unknown'
	});

	// 搜索相关
	const searchText = ref('');

	// 字体大小管理
	const currentFontScale = ref(1);

	// 动态字体样式
	const dynamicFontStyle = computed(() => ({
		'--font-scale': currentFontScale.value
	}));

	// 初始化字体大小
	const initFontSize = () => {
		try {
			const savedFontScale = uni.getStorageSync('fontScale') || 1;
			currentFontScale.value = savedFontScale;
			console.info(`📄 [IMAGES] 页面字体大小初始化: 缩放 ${savedFontScale}`);
		} catch (error) {
			console.error('页面字体大小初始化失败:', error);
		}
	};

	// 处理字体大小变化
	const handleFontSizeChange = (data: any) => {
		const { size, scale } = data;
		currentFontScale.value = scale;
		console.info(`📄 [IMAGES] 页面字体大小已更新: ${size} (缩放: ${scale})`);
	};

	// 定期更新上传状态
	const updateUploadStatus = () => {
		uploadStatus.value = getUploadStatus();
	};

	// 过滤图片列表
	const taskLocalPendingmages = computed(() => {
		if (!searchText.value) {
			return localImages.value;
		}

		const searchLower = searchText.value.toLowerCase();
		return localImages.value.filter((image: any) => {
			const taskInfo = getTaskInfo(image.taskId);
			if (!taskInfo) return false;

			// 搜索条件：小区名称、地址、派单名称、点位编号等
			return (
				// 小区名称
				(taskInfo.zoneName && taskInfo.zoneName.toLowerCase().includes(searchLower)) ||
				// 地址
				(taskInfo.zoneAddress && taskInfo.zoneAddress.toLowerCase().includes(searchLower)) ||
				// 派单名称
				(getTaskDeliveryNameById(image.taskId) &&
				 (typeof getTaskDeliveryNameById(image.taskId) === 'string' ?
				  getTaskDeliveryNameById(image.taskId).toLowerCase().includes(searchLower) :
				  getTaskDeliveryNameById(image.taskId).some((name: string) => name.toLowerCase().includes(searchLower)))) ||
				// 点位编号
				(taskInfo.spotCode && taskInfo.spotCode.toLowerCase().includes(searchLower)) ||
				// 点位名称
				(taskInfo.spotName && taskInfo.spotName.toLowerCase().includes(searchLower)) ||
				// 区域
				(taskInfo.belongDistrict && taskInfo.belongDistrict.toLowerCase().includes(searchLower))
			);
		});
	});

	// 处理搜索
	const onSearchClick = (text: string) => {
		searchText.value = text || '';
	};

// 处理菜单点击
const onMenuClick = () => {
	// 这里可以添加菜单点击的处理逻辑
	console.info('Menu clicked');
};

// 处理下拉菜单点击
const toggleDropdown = () => {
	// 这里可以添加下拉菜单的处理逻辑
	console.info('Dropdown toggled');
};

	// 启动定时器，每秒更新一次上传状态
	let statusTimer: any = null;
	onMounted(() => {
		// 初始化字体大小
		initFontSize();

		// 监听字体大小变化
		uni.$on('fontSizeChanged', handleFontSizeChange);

		statusTimer = setInterval(updateUploadStatus, 1000);

		// 初始获取一次上传状态
		updateUploadStatus();

		// 清理定时器
		return () => {
			if (statusTimer) {
				clearInterval(statusTimer);
			}
		};
	});

	// 页面卸载时清理事件监听
	onUnmounted(() => {
		// 清理字体大小变化事件监听
		uni.$off('fontSizeChanged', handleFontSizeChange);

		// 清理定时器
		if (statusTimer) {
			clearInterval(statusTimer);
		}
	});

	// 获取任务信息（从图片中的保存的任务信息）
	const getTaskInfo = (taskId : string | number) => {
		// 查找对应的图片
		const image = localImages.value.find((img: any) => img.taskId === taskId);

		// 如果找到图片并且有保存的任务信息，直接返回
		if (image && image.taskInfo) {
			return image.taskInfo;
		}

		// 如果图片中没有保存任务信息（兼容旧数据），尝试从任务列表中查找
		const task = tasks.value.find((t: any) => t.taskId === taskId);
		if (task) return task;

		// 如果仍然没有找到，尝试在折叠任务中查找
		for (const mainTask of tasks.value) {
			if (mainTask.collapsedZoneTasks && mainTask.collapsedZoneTasks.length > 0) {
				const collapsedTask = mainTask.collapsedZoneTasks.find((t: any) => t.taskId === taskId);
				if (collapsedTask) {
					// 返回折叠任务，但标记它是折叠的
					return {
						...collapsedTask,
						isCollapsed: true,
						mainTaskId: mainTask.taskId,
						// 复制主任务的关键信息
						mainTask: {
							taskId: mainTask.taskId,
							zoneName: mainTask.zoneName,
							zoneId: mainTask.zoneId,
							zoneAddress: mainTask.zoneAddress,
							belongDistrict: mainTask.belongDistrict,
							deliveryId: mainTask.deliveryId
						}
					};
				}
			}
		}

		// 如果仍然没有找到，返回 undefined
		return undefined;
	};

	// 获取拍摄位置信息
	const getShotLocation = (task: any) => {
		if (!task) return '未知位置';

		let location = '';

		// 添加区域信息
		if (task.belongDistrict) {
			location += task.belongDistrict;
		}

		// 添加小区信息
		if (task.zoneName) {
			location += (location ? ' ' : '') + task.zoneName;
		}

		// 添加具体位置信息
		if (task.spotId && task.spotName) {
			// 点位任务
			location += (location ? ' ' : '') + task.spotName;
		} else if (task.zonePhotoAddress) {
			// 区域任务
			location += (location ? ' ' : '') + task.zonePhotoAddress;
		}

		return location || '未知位置';
	};

	// 计算上传中的图片数量
	const uploadingCount = computed(() => {
		return localImages.value.filter((img: any) => img.status === 0).length;
	});

	// 处理单张图片上传
	const handleUpload = async (image : any) => {
		if (image.imageStatus !== 'pending') return;

		// 检查网络状态
		const canUpload = await imagesStore.checkNetworkStatus();
		if (!canUpload) return;

		try {
			const result = await uploadTaskImage(image);
			if (result) {
				showToast('上传成功', 'success');
			} else {
				showToast('上传失败', 'error');
			}
		} catch (error) {
			console.error('上传失败:', error);
			showToast('上传失败', 'error');
		}
	};

	// 处理上传按钮点击（开始/暂停/恢复）
	const handleUploadToggle = async () => {
		// 获取最新的上传状态
		updateUploadStatus();

		if (uploadStatus.value.isUploading) {
			// 如果正在上传，则根据暂停状态决定暂停或恢复
			if (uploadStatus.value.isPaused) {
				// 如果已暂停，则恢复上传
				resumeUpload();
			} else {
				// 如果正在上传，则暂停上传
				pauseUpload();
			}
		} else {
			// 如果没有上传任务，则开始新的上传
			startUpload();
		}
	};

	// 处理图片删除
	const handleDelete = (image : any) => {
		uni.showModal({
			title: '确认删除',
			content: '确定要删除这张图片吗？',
			confirmText: '确定',
			cancelText: '取消',
			success: (res: any) => {
				if (res.confirm) {
					if (deleteLocalImage(image.imageUrl)) {
						showToast('删除成功', 'success');
					} else {
						showToast('删除失败', 'error');
					}
				}
			}
		});
	};

	// 处理图片点击 - 显示操作菜单
	const handleImageClick = (image: any) => {
		// 构建菜单选项
		const menuItems = ['查看', '单独上传', '保存到相册', '删除', '取消'];

		// 如果图片已上传，禁用单独上传选项
		if (image.imageStatus !== 'pending') {
			// 将"单独上传"替换为"已上传"（禁用状态）
			menuItems[1] = '已上传';
		}

		uni.showActionSheet({
			itemList: menuItems,
			itemColor: '#007AFF',
			success: (res: any) => {
				switch (res.tapIndex) {
					case 0: // 查看
						handleViewImage(image);
						break;
					case 1: // 单独上传
						if (image.imageStatus === 'pending') {
							handleUpload(image);
						} else {
							showToast('图片已上传', 'none');
						}
						break;
					case 2: // 保存到相册
						handleSaveToAlbum(image);
						break;
					case 3: // 删除
						handleDelete(image);
						break;
					case 4: // 取消
						// 用户取消，不执行任何操作
						break;
				}
			},
			fail: (res: any) => {
				console.info('用户取消操作');
			}
		});
	};

	// 查看图片
	const handleViewImage = (image: any) => {
		uni.previewImage({
			urls: [image.imageUrl],
			current: image.imageUrl
		});
	};

	// 保存图片到相册
	const handleSaveToAlbum = (image: any) => {
		uni.showLoading({ title: '保存中...' });

		// #ifdef APP-PLUS
		// 原生App环境下使用saveImageToPhotosAlbum
		uni.saveImageToPhotosAlbum({
			filePath: image.imageUrl,
			success: () => {
				showToast('保存成功', 'success');
			},
			fail: (err: any) => {
				console.error('保存失败:', err);
				showToast('保存失败', 'error');
			},
			complete: () => {
				uni.hideLoading();
			}
		});
		// #endif

		// #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO
		// 小程序环境下使用saveImageToPhotosAlbum
		uni.saveImageToPhotosAlbum({
			filePath: image.imageUrl,
			success: () => {
				showToast('保存成功', 'success');
			},
			fail: (err: any) => {
				console.error('保存失败:', err);
				showToast('保存失败', 'error');
			},
			complete: () => {
				uni.hideLoading();
			}
		});
		// #endif

		// #ifdef H5
		// H5环境下使用canvas转换后下载
		try {
			// 创建一个Image对象加载图片
			const img = new Image();
			img.crossOrigin = 'anonymous'; // 处理跨域问题
			img.onload = () => {
				// 创建canvas
				const canvas = document.createElement('canvas');
				canvas.width = img.width;
				canvas.height = img.height;
				const ctx = canvas.getContext('2d');

				// 将图片绘制到canvas
				if (ctx) {
					ctx.drawImage(img, 0, 0, img.width, img.height);

					// 将canvas转为blob并下载
					canvas.toBlob((blob) => {
						if (blob) {
							const url = URL.createObjectURL(blob);
							const a = document.createElement('a');
							a.href = url;
							a.download = `task_image_${new Date().getTime()}.jpg`;
							document.body.appendChild(a);
							a.click();
							document.body.removeChild(a);
							URL.revokeObjectURL(url);
							showToast('图片已准备下载', 'success');
						} else {
							showToast('图片处理失败', 'error');
						}
						uni.hideLoading();
					}, 'image/jpeg', 0.9);
				} else {
					showToast('浏览器不支持Canvas', 'error');
					uni.hideLoading();
				}
			};

			img.onerror = () => {
				console.error('图片加载失败');
				showToast('图片加载失败', 'error');
				uni.hideLoading();
			};

			// 开始加载图片
			img.src = image.imageUrl;

			// 如果图片已经缓存，可能不会触发onload
			if (img.complete) {
				// 手动触发onload事件处理函数
				const event = new Event('load');
				img.dispatchEvent(event);
			}
		} catch (err) {
			console.error('下载失败:', err);
			showToast('下载失败', 'error');
			uni.hideLoading();
		}
		// #endif
	};

	onMounted(async () => {
		const authStore = useAuthStore();
		if (!await authStore.checkAuth()) {
			uni.reLaunch({
				url: '/pages/login/login'
			});
		}
	});
</script>

<style lang="scss" scoped>
	.images-container {
		min-height: 100vh;
		background-color: $uni-bg-color-grey;

		// 应用字体缩放到所有文本元素
		font-size: calc(16px * var(--font-scale, 1));

		.upload-progress-bar {
			position: fixed;
			left: 0;
			right: 0;
			height: 60px;
			background-color: #ffffff;
			border-bottom: 1px solid #e0e0e0;
			z-index: 998;
			display: flex;
			align-items: center;
			padding: 0 15px;

			.progress-content {
				width: 100%;

				.progress-info {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8px;

					.progress-text {
						font-size: calc(14px * var(--font-scale, 1));
						color: #333333;
						font-weight: 500;
					}

					.progress-percentage {
						font-size: calc(14px * var(--font-scale, 1));
						color: #2196F3;
						font-weight: 600;
					}
				}

				.progress-track {
					width: 100%;
					height: 4px;
					background-color: #e0e0e0;
					border-radius: 2px;
					overflow: hidden;

					.progress-fill {
						height: 100%;
						background: linear-gradient(90deg, #2196F3 0%, #21CBF3 100%);
						border-radius: 2px;
						transition: width 0.3s ease;
					}
				}
			}
		}

		.images-list {
			height: 100vh;

			/* 隐藏滚动条 */
			&::-webkit-scrollbar {
				display: none;
			}
			scrollbar-width: none; /* Firefox */
			-ms-overflow-style: none; /* IE and Edge */
		}

		.content {
			height: calc(100vh - 120rpx);

			/* 隐藏滚动条 */
			&::-webkit-scrollbar {
				display: none;
			}
			scrollbar-width: none; /* Firefox */
			-ms-overflow-style: none; /* IE and Edge */
		}

		.empty-state {
			padding: $uni-spacing-row-lg;
			text-align: center;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 300rpx;
			margin-top: 100rpx;

			.empty-icon {
				font-size: calc(80rpx * var(--font-scale, 1));
				margin-bottom: 20rpx;
			}

			.empty-text {
				color: $uni-text-color-grey;
				font-size: calc($uni-font-size-lg * var(--font-scale, 1));
				font-weight: 500;
				margin-top: 20rpx;
			}
		}

		.image-list {
			padding: 0;
			margin: 0;
			width: 100%;

			.image-item {
				background-color: $uni-bg-color;
				padding: 1.5rem 0.1rem;
				margin-top: 0.2rem;
				margin-bottom: 0.1rem;
				border-bottom: 1px solid rgba(0, 0, 0, 0.05);
				display: flex;
				gap: $uni-spacing-row-lg;
				transition: all 0.2s ease;
				cursor: pointer;
				position: relative;

				/* 添加点击提示 */
				&::after {
					content: '点击查看操作选项';
					position: absolute;
					top: 50%;
					right: 15px;
					transform: translateY(-50%);
					font-size: calc(24rpx * var(--font-scale, 1));
					color: $uni-text-color-grey;
					opacity: 0.6;
					pointer-events: none;
				}

				&:active {
					transform: translateY(1px);
					background-color: rgba(0, 0, 0, 0.02);
				}

				&:hover {
					background-color: rgba(0, 0, 0, 0.01);
				}

				.image-preview {
					position: relative;
					width: 240rpx;
					flex-shrink: 0;
					display: flex;
					flex-direction: column;

					.preview-image {
						width: 100%;
						height: 340rpx;
						border-radius: $uni-border-radius-base;
						object-fit: cover;
					}

					.image-status {
						position: absolute;
						top: $uni-spacing-row-base;
						right: $uni-spacing-row-base;
						padding: $uni-spacing-row-sm $uni-spacing-row-base;
						border-radius: $uni-border-radius-sm;
						background-color: $uni-bg-color-mask;
						color: $uni-text-color-inverse;
						font-size: calc($uni-font-size-sm * var(--font-scale, 1));
						font-weight: 600;
						box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

						&.success {
							background-color: rgba($uni-color-success, 0.8);
						}

						&.pending {
							background-color: rgba($uni-color-warning, 0.8);
						}
					}


				}

				.image-content {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.image-info {
						.info-header {
							margin-bottom: $uni-spacing-row-lg;
							display: flex;
							flex-direction: column;

							.info-title {
								font-size: calc($uni-font-size-lg * var(--font-scale, 1));
								font-weight: 700;
								color: $uni-text-color;
							}

							.collapsed-badge {
								font-size: calc($uni-font-size-sm * var(--font-scale, 1));
								color: $uni-color-warning;
								font-weight: 600;
								margin-top: 4rpx;
							}

							.merged-count {
								font-size: calc(24rpx * var(--font-scale, 1));
								color: #ff6b00;
								margin-left: 8rpx;
								font-weight: normal;
								margin-top: 4rpx;
							}
						}

						.info-row {
							display: flex;
							margin-bottom: $uni-spacing-col-sm;
							font-size: calc($uni-font-size-base * var(--font-scale, 1));
							line-height: 1.6;

							&.delivery-row {
								margin-top: 8rpx;
								margin-bottom: 8rpx;
							}

							.label {
								color: $uni-text-color-grey;
								width: 100rpx;
								font-weight: 500;
								font-size: calc($uni-font-size-base * var(--font-scale, 1));
							}

							.value {
								color: $uni-text-color;
								flex: 1;
								font-weight: 500;
								font-size: calc($uni-font-size-base * var(--font-scale, 1));
							}

							.value-container {
								flex: 1;
								display: flex;
								flex-direction: column;

								.delivery-item {
									color: $uni-color-primary;
									font-weight: 500;
									margin-bottom: 4rpx;
									font-size: calc(26rpx * var(--font-scale, 1));
									line-height: 1.4;

									&:last-child {
										margin-bottom: 0;
									}
								}
							}
						}
					}


				}
			}
		}
	}
</style>