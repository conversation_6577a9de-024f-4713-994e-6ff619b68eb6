// 声明uni全局对象，解决TypeScript错误
declare const uni: any;

// 导入类型和store
import { type TaskImages } from '../types';
import { useTaskStore } from '../stores/task';
import { useAuthStore } from '../stores/auth';

/**
 * 任务工具类 - 提供任务相关的工具方法
 */
export class TaskUtils {
	/**
	 * 获取地理位置信息
	 * @returns Promise 包含经纬度信息
	 */
	static getLocation(): Promise<{ latitude: number; longitude: number }> {
		const taskStore = useTaskStore();
		if (!taskStore.isLocationAvailable()) {
			return Promise.reject(new Error('位置服务不可用'));
		}

		return new Promise((resolve, reject) => {
			uni.getLocation({
				type: 'gcj02',
				success: (res: any) => resolve({ latitude: res.latitude, longitude: res.longitude }),
				fail: (error: any) => {
					taskStore.setLocationAvailable(false);
					reject(error);
				},
			});
		});
	}

	/**
	 * 拍照并获取临时文件路径
	 * @returns Promise 包含图片临时路径
	 */
	static takePhoto(): Promise<string> {
		return new Promise((resolve, reject) => {
			uni.chooseImage({
				count: 1,
				sourceType: ['camera'],
				success: (res: any) => resolve(res.tempFilePaths[0]),
				fail: reject,
			});
		});
	}

	/**
	 * 从相册选择图片并获取临时文件路径
	 * @returns Promise 包含图片临时路径
	 */
	static chooseFromAlbum(): Promise<string> {
		return new Promise((resolve, reject) => {
			uni.chooseImage({
				count: 1,
				sourceType: ['album'],
				success: (res: any) => resolve(res.tempFilePaths[0]),
				fail: reject,
			});
		});
	}

	/**
	 * 根据任务的onlyPhoto属性选择拍照方式
	 * @param task 任务对象
	 * @returns Promise 包含图片临时路径
	 */
	static chooseImageSource(task: any): Promise<string> {
		// 如果任务要求只能拍照，直接调用拍照功能
		if (task.onlyPhoto === true) {
			return this.takePhoto();
		}

		// 否则，弹出选择框让用户选择
		return new Promise((resolve, reject) => {
			uni.showActionSheet({
				itemList: ['拍照', '从相册选择'],
				success: async (res: any) => {
					try {
						if (res.tapIndex === 0) {
							// 用户选择拍照
							const imageUrl = await this.takePhoto();
							resolve(imageUrl);
						} else {
							// 用户选择从相册选择
							const imageUrl = await this.chooseFromAlbum();
							resolve(imageUrl);
						}
					} catch (error) {
						reject(error);
					}
				},
				fail: reject,
			});
		});
	}

	/**
	 * 生成唯一的本地ID
	 * @returns 唯一的本地ID字符串
	 */
	static generateLocalId(): string {
		// 使用时间戳 + 随机数生成唯一ID
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 11);
		return `local_${timestamp}_${random}`;
	}

	/**
	 * 创建任务图片对象
	 * @param params 创建图片所需参数
	 * @returns 任务图片对象
	 */
	static createTaskImage({
		taskId,
		zoneId,
		spotId,
		spotCode,
		imageUrl,
		location,
		task,
	}: {
		taskId: string | number;
		zoneId?: string | number;
		spotId?: string | number | undefined;
		spotCode?: string;
		imageUrl: string;
		location?: { latitude: number; longitude: number };
		task?: any;
	}): TaskImages {
		const authStore = useAuthStore();
		const taskStore = useTaskStore();
		const username = authStore.getUsername();
		const now = new Date();
		const timestamp = Date.now();

		// 获取完整的地址信息
		const shotLocation = task ? taskStore.getShotLocation(task) : '未设置';

		// 创建完整的TaskImages对象
		return {
			id: this.generateLocalId(), // 生成唯一的本地ID
			taskId,
			zoneId,
			spotId,
			imageStatus: 'pending',
			imageName: `task_${taskId}_${timestamp}.jpg`,
			uploadUser: username,
			fileName: imageUrl?.split('/').pop() || '',
			imageUrl,
			spotCode,
			shotTime: now,
			shotLocation: shotLocation.trim(),
			imageLongitude: location?.longitude?.toString(),
			imageLatitude: location?.latitude?.toString(),
			status: 0,
		};
	}
}

// 只导出 TaskUtils 类，不再导出单独的函数
