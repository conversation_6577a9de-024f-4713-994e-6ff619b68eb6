/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #4A90E2;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#2C405A;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#8C9BAA;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #A4B8D3;
$uni-text-color-disable:#c0c0c0;

/* 字体粗细 */
$uni-font-weight-light: 300;
$uni-font-weight-normal: 400;
$uni-font-weight-medium: 500;
$uni-font-weight-semibold: 600;
$uni-font-weight-bold: 700;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#EBF3FF;
$uni-bg-color-hover:#F5F9FF;//点击状态颜色
$uni-bg-color-mask:rgba(74, 144, 226, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#D9E6F7;

/* 尺寸变量 */

/* 文字尺寸 - 针对30-45岁人群优化 */
$uni-font-size-sm:14px;   // 小字体，保持信息密度
$uni-font-size-base:15px; // 基础字体，标准可读性
$uni-font-size-lg:16px;   // 大字体，重要信息突出

/* 图片尺寸 */
$uni-img-size-sm:20px;
$uni-img-size-base:26px;
$uni-img-size-lg:40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 - 针对30-45岁人群优化 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:20px;  // 标题字体，保持层级感
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:26px;  // 副标题字体，适中大小
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:15px; // 段落字体，标准可读性

/* 30-45岁人群专用样式变量 */
// 重要信息字体大小 - 追求效率和专业感
$uni-font-size-important: 17px;     // 重要信息字体，适中突出
$uni-font-size-emphasis: 19px;      // 强调信息字体，明显但不过大
$uni-font-size-critical: 22px;      // 关键信息字体，显著突出

// 行高优化 - 平衡可读性和信息密度
$uni-line-height-tight: 1.2;       // 紧凑行高，提高信息密度
$uni-line-height-normal: 1.4;      // 标准行高，平衡可读性
$uni-line-height-loose: 1.6;       // 宽松行高，重要内容使用

// 字体粗细优化 - 现代专业感
$uni-font-weight-readable: 400;     // 标准可读性字体粗细
$uni-font-weight-emphasis: 500;     // 强调文本的字体粗细
$uni-font-weight-strong: 600;       // 重要信息的字体粗细

// 间距优化 - 高效操作体验
$uni-touch-target-min: 40px;       // 最小触控目标尺寸，适合精准操作
$uni-spacing-efficient: 12px;       // 高效间距，提高信息密度
$uni-spacing-comfortable: 16px;     // 舒适间距，标准使用
$uni-spacing-generous: 20px;        // 宽松间距，重要区域使用

// 现代化颜色方案
$uni-text-color-primary: #1f2937;    // 主要文本颜色，现代感
$uni-text-color-secondary: #6b7280;  // 次要文本颜色
$uni-bg-color-modern: #ffffff;       // 现代化背景色
$uni-border-color-subtle: #e5e7eb;   // 精细边框色
