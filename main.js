import App from './App'

// 导入环境配置和生产环境优化
import { ENVIRONMENT } from './utils/env'
import './utils/security' // 自动执行安全检查
import './utils/production' // 自动执行生产环境配置
import './utils/debug-service-fix' // 修复调试服务连接问题

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 生产环境配置
if (ENVIRONMENT.isProduction) {
  Vue.config.productionTip = false;
  Vue.config.devtools = false;
  Vue.config.debug = false;
  Vue.config.silent = true;
} else {
  Vue.config.productionTip = false;
}

App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'

// 导入全局组件
import DeliveryNavbar from './pages/delivery/components/delivery-navbar.vue'
import TaskNavbar from './pages/task/components/task-navbar.vue'
import ImagesNavbar from './pages/images/components/images-navbar.vue'
import TaskFilter from './pages/task/components/task-filter.vue'

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()

  // 生产环境配置
  if (ENVIRONMENT.isProduction) {
    // 禁用开发工具
    app.config.devtools = false;

    // 全局错误处理
    app.config.errorHandler = (error, _instance, info) => {
      console.error('[Vue Error]:', error, info);
      // 这里可以添加错误上报逻辑
    };

    // 警告处理
    app.config.warnHandler = (msg, _instance, trace) => {
      // 生产环境静默处理警告
      if (!ENVIRONMENT.isProduction) {
        console.warn('[Vue Warn]:', msg, trace);
      }
    };
  }

  // 注册全局组件
  app.component('DeliveryNavbar', DeliveryNavbar)
  app.component('TaskNavbar', TaskNavbar)
  app.component('ImagesNavbar', ImagesNavbar)
  app.component('TaskFilter', TaskFilter)

  app.use(pinia)
  return {
    app
  }
}
// #endif