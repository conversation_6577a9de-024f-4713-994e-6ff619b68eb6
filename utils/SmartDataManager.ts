import { smartDataLoader } from './SmartDataLoader';
import { dataAnalysisEngine, type AnalysisResult, type StatusChange } from './DataAnalysisEngine';
import { smartStateManager, type TaskStatusUpdate } from './SmartStateManager';
import { cacheManager } from './CacheManager';
import { performanceMonitor } from './PerformanceMonitor';
import type { Task, Delivery } from '@/types';

/**
 * 数据管理状态
 */
export interface DataManagerState {
	isInitialized: boolean;
	isLoading: boolean;
	lastLoadTime: number | null;
	lastLoadUser: string | null;
	dataVersion: string;
	analysisResult: AnalysisResult | null;
}

/**
 * 加载进度回调
 */
export type LoadProgressCallback = (stage: string, progress: number) => void;

/**
 * 智能数据管理系统
 * 集成所有数据管理组件，提供统一的数据管理接口
 */
export class SmartDataManager {
	private state: DataManagerState = {
		isInitialized: false,
		isLoading: false,
		lastLoadTime: null,
		lastLoadUser: null,
		dataVersion: '1.0.0',
		analysisResult: null
	};

	private stateChangeUnsubscribe: (() => void) | null = null;

	constructor() {
		this.initialize();
	}

	/**
	 * 初始化数据管理系统
	 */
	private async initialize(): Promise<void> {
		console.info('🚀 [DATA MANAGER] 初始化智能数据管理系统');
		
		try {
			// 1. 设置性能监控
			this.setupPerformanceMonitoring();

			// 2. 订阅状态变化
			this.subscribeToStateChanges();

			// 3. 尝试从缓存恢复分析结果
			await this.restoreAnalysisFromCache();

			// 4. 更新状态
			this.updateState({
				isInitialized: true,
				lastLoadTime: smartDataLoader.getLastLoadTime(),
				lastLoadUser: smartDataLoader.getLastLoadUser()
			});

			console.info('✅ [DATA MANAGER] 智能数据管理系统初始化完成');
		} catch (error) {
			console.error('❌ [DATA MANAGER] 初始化失败:', error);
			throw error;
		}
	}

	/**
	 * 检查并加载数据
	 */
	async checkAndLoadData(
		currentUser: string,
		loadDeliveries: () => Promise<void>,
		loadTasks: () => Promise<void>,
		onProgress?: LoadProgressCallback
	): Promise<boolean> {
		console.info(`🔍 [DATA MANAGER] 检查数据加载需求，用户: ${currentUser}`);

		// 检查是否需要加载数据
		const shouldLoad = smartDataLoader.shouldLoadData(currentUser);
		
		if (!shouldLoad) {
			console.info('✅ [DATA MANAGER] 数据已是最新，无需重新加载');
			return false;
		}

		// 执行数据加载
		await this.forceLoadData(currentUser, loadDeliveries, loadTasks, onProgress);
		return true;
	}

	/**
	 * 强制加载数据
	 */
	async forceLoadData(
		currentUser: string,
		loadDeliveries: () => Promise<void>,
		loadTasks: () => Promise<void>,
		onProgress?: LoadProgressCallback
	): Promise<void> {
		if (this.state.isLoading) {
			console.warn('⚠️ [DATA MANAGER] 数据正在加载中，请等待');
			return;
		}

		console.info('🚀 [DATA MANAGER] 开始强制加载数据');
		
		this.updateState({ isLoading: true });

		try {
			await performanceMonitor.measure(
				'full-data-load',
				'data-loading',
				async () => {
					// 使用智能数据加载器加载数据
					await smartDataLoader.forceLoadData(
						currentUser,
						loadDeliveries,
						loadTasks,
						onProgress
					);
				},
				{ user: currentUser }
			);

			// 更新状态
			this.updateState({
				lastLoadTime: smartDataLoader.getLastLoadTime(),
				lastLoadUser: currentUser
			});

			console.info('✅ [DATA MANAGER] 数据加载完成');
		} catch (error) {
			console.error('❌ [DATA MANAGER] 数据加载失败:', error);
			throw error;
		} finally {
			this.updateState({ isLoading: false });
		}
	}

	/**
	 * 执行数据分析
	 */
	async performDataAnalysis(tasks: Task[], deliveries: Delivery[]): Promise<AnalysisResult> {
		console.info('🔍 [DATA MANAGER] 开始执行数据分析');

		const analysisResult = await performanceMonitor.measure(
			'data-analysis',
			'computation',
			async () => {
				return await dataAnalysisEngine.performFullAnalysis(tasks, deliveries);
			},
			{ 
				taskCount: tasks.length, 
				deliveryCount: deliveries.length 
			}
		);

		// 更新状态
		this.updateState({ analysisResult });

		// 缓存分析结果
		cacheManager.set(
			'analysis_result',
			analysisResult,
			{
				persistent: true,
				expireTime: 24 * 60 * 60 * 1000, // 24小时过期
				version: this.state.dataVersion,
				dependencies: ['tasks', 'deliveries']
			}
		);

		console.info('✅ [DATA MANAGER] 数据分析完成');
		return analysisResult;
	}

	/**
	 * 更新任务状态
	 */
	async updateTaskStatus(taskId: string, statusUpdate: Partial<TaskStatusUpdate>): Promise<void> {
		console.info(`🔄 [DATA MANAGER] 更新任务状态: ${taskId}`);

		await performanceMonitor.measure(
			'task-status-update',
			'user-interaction',
			async () => {
				await smartStateManager.updateTaskStatus(taskId, statusUpdate);
			},
			{ taskId, updateType: Object.keys(statusUpdate).join(',') }
		);
	}

	/**
	 * 批量更新任务状态
	 */
	async batchUpdateTaskStatus(updates: TaskStatusUpdate[]): Promise<void> {
		console.info(`🔄 [DATA MANAGER] 批量更新任务状态，数量: ${updates.length}`);

		await performanceMonitor.measure(
			'batch-task-status-update',
			'computation',
			async () => {
				await smartStateManager.batchUpdateStatus(updates);
			},
			{ updateCount: updates.length }
		);
	}

	/**
	 * 获取缓存数据
	 */
	getCachedData<T>(key: string): T | null {
		return cacheManager.get<T>(key);
	}

	/**
	 * 设置缓存数据
	 */
	setCachedData<T>(key: string, data: T, options?: {
		expireTime?: number;
		persistent?: boolean;
		dependencies?: string[];
	}): void {
		cacheManager.set(key, data, {
			version: this.state.dataVersion,
			...options
		});
	}

	/**
	 * 失效相关缓存
	 */
	invalidateCache(dependency: string): void {
		const invalidatedCount = cacheManager.invalidateByDependency(dependency);
		console.info(`🗑️ [DATA MANAGER] 失效了 ${invalidatedCount} 个相关缓存项`);
	}

	/**
	 * 获取数据管理状态
	 */
	getState(): DataManagerState {
		return { ...this.state };
	}

	/**
	 * 获取性能统计
	 */
	getPerformanceStats() {
		return performanceMonitor.getStats();
	}

	/**
	 * 获取缓存统计
	 */
	getCacheStats() {
		return cacheManager.getStats();
	}

	/**
	 * 生成性能报告
	 */
	generatePerformanceReport(): string {
		return performanceMonitor.generateReport();
	}

	/**
	 * 清除所有数据和缓存
	 */
	clearAllData(): void {
		console.info('🗑️ [DATA MANAGER] 清除所有数据和缓存');

		// 清除加载记录
		smartDataLoader.clearLoadRecord();

		// 清除分析缓存
		dataAnalysisEngine.clearAnalysisCache();

		// 清除状态管理器
		smartStateManager.clearPendingUpdates();

		// 清除缓存
		cacheManager.clear();

		// 清除性能数据
		performanceMonitor.clear();

		// 重置状态
		this.updateState({
			lastLoadTime: null,
			lastLoadUser: null,
			analysisResult: null
		});

		console.info('✅ [DATA MANAGER] 所有数据和缓存已清除');
	}

	/**
	 * 设置性能监控
	 */
	private setupPerformanceMonitoring(): void {
		// 设置性能阈值
		performanceMonitor.setThresholds({
			dataLoading: 5000, // 5秒
			rendering: 100, // 100毫秒
			computation: 1000, // 1秒
			userInteraction: 50, // 50毫秒
			network: 10000 // 10秒
		});

		// 订阅性能警告
		performanceMonitor.onWarning((warning) => {
			console.warn(`⚠️ [PERFORMANCE WARNING] ${warning.message}`);
			
			// 可以在这里添加更多的警告处理逻辑
			// 比如发送到监控系统、显示用户提示等
		});
	}

	/**
	 * 订阅状态变化
	 */
	private subscribeToStateChanges(): void {
		this.stateChangeUnsubscribe = smartStateManager.subscribeToChanges((changes: StatusChange[]) => {
			console.info(`📡 [DATA MANAGER] 收到 ${changes.length} 个状态变化`);

			// 更新分析结果
			if (this.state.analysisResult) {
				dataAnalysisEngine.updateAnalysis(changes);
				this.updateState({ analysisResult: dataAnalysisEngine.getAnalysisResult() });
			}

			// 失效相关缓存
			const affectedData = smartStateManager.getAffectedData(changes);
			affectedData.cacheKeys.forEach(key => {
				cacheManager.delete(key);
			});
		});
	}

	/**
	 * 从缓存恢复分析结果
	 */
	private async restoreAnalysisFromCache(): Promise<void> {
		try {
			const cachedAnalysis = dataAnalysisEngine.loadAnalysisFromCache();
			if (cachedAnalysis) {
				this.updateState({ analysisResult: cachedAnalysis });
				console.info('✅ [DATA MANAGER] 从缓存恢复分析结果');
			}
		} catch (error) {
			console.warn('⚠️ [DATA MANAGER] 从缓存恢复分析结果失败:', error);
		}
	}

	/**
	 * 更新状态
	 */
	private updateState(updates: Partial<DataManagerState>): void {
		this.state = { ...this.state, ...updates };
	}

	/**
	 * 销毁数据管理系统
	 */
	destroy(): void {
		console.info('🗑️ [DATA MANAGER] 销毁智能数据管理系统');

		// 取消状态变化订阅
		if (this.stateChangeUnsubscribe) {
			this.stateChangeUnsubscribe();
		}

		// 清除待处理的更新
		smartStateManager.clearPendingUpdates();

		// 停止缓存管理器
		cacheManager.stopCleanupTimer();

		console.info('✅ [DATA MANAGER] 智能数据管理系统已销毁');
	}
}

// 创建全局实例
export const smartDataManager = new SmartDataManager();
