/**
 * 格式化日期为 YYYY-MM-DD
 * @param date 日期对象或字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date : Date | string | undefined) => {
	if (!date) return '';
	const d = new Date(date);
	return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

/**
 * 格式化日期时间为 YYYY-MM-DD HH:mm:ss
 * @param date 日期对象或字符串
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date : Date | string | undefined) => {
	if (!date) return '';
	const d = new Date(date);
	return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}:${String(d.getSeconds()).padStart(2, '0')}`;
};

Date.prototype.toJSON = function () {
	if (!this) return '';
	return formatDateTime(this);
};

/**
 * 根据时间范围类型计算开始和结束日期
 * @param orderPeriod 时间范围类型
 * @returns 包含开始和结束日期的对象
 */
export const getDateRange = (orderPeriod: 'today' | 'week' | 'halfMonth' | 'month') => {
	const today = new Date();
	today.setHours(0, 0, 0, 0); // 设置为当天的开始时间

	let startDay: Date;
	let endDay: Date;

	switch (orderPeriod) {
		case 'today':
			// 当天：startDay：today endDay today+1
			startDay = new Date(today);
			endDay = new Date(today);
			endDay.setDate(endDay.getDate() + 1);
			break;

		case 'week':
			// week：startDay: today -7, endDay: today+7
			startDay = new Date(today);
			startDay.setDate(startDay.getDate() - 7);
			endDay = new Date(today);
			endDay.setDate(endDay.getDate() + 7);
			break;

		case 'halfMonth':
			// halfMonth: startDay: today-15, endDay: today+15
			startDay = new Date(today);
			startDay.setDate(startDay.getDate() - 15);
			endDay = new Date(today);
			endDay.setDate(endDay.getDate() + 15);
			break;

		case 'month':
			// month: startDay: today-30, endDay: today +30
			startDay = new Date(today);
			startDay.setDate(startDay.getDate() - 30);
			endDay = new Date(today);
			endDay.setDate(endDay.getDate() + 30);
			break;

		default:
			// 默认使用week
			startDay = new Date(today);
			startDay.setDate(startDay.getDate() - 7);
			endDay = new Date(today);
			endDay.setDate(endDay.getDate() + 7);
			break;
	}

	return {
		startDay: formatDate(startDay),
		endDay: formatDate(endDay)
	};
};