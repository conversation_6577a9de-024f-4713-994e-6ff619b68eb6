import { Task } from './Task';

/** OMaps —— Delivery, 派单信息 */
export interface Delivery {
	/** 主键 */
	deliveryId?: number | string;
	/** 订单ID */
	orderId?: number | string;
	/** 发布Id */
	itemId?: number | string;
	/** 派单类型 up: 上刊, down: 下刊, monitor: 监测 */
	deliveryType?: 'up' | 'down' | 'monitor';
	/** 派单名称 */
	deliveryName?: string;
	/** 派单时间 */
	deliveryDate?: Date;
	/** 画面送达时间 */
	imageReceiveDate?: Date;
	/** 筛选点位包，会筛选选取的点位包与上刊点位包之间的交集作为点位集合 */
	filterPacketId?: number | string;
	/** 筛选媒体，会筛选选取的媒体与上刊媒体之间的交集作为媒体集合 */
	filterMediaId?: number | string;
	/** 点位筛选，与任务时间配合使用，通过点位的上刊状态进行筛选 */
	spotPublicationState?: number | string;
	/** 选点方式 */
	selectPointWay?: number | string;
	/** 已选点位 */
	selectedPointCount?: number | string;
	/** 开始分组级别，在上刊 APP 中从哪一个级别开始分组。例如：从"楼盘名称"开始分组，那么将不提供路名（如果有）的分组功能 */
	startGroupLevel?: number | string;
	/** 小区拍摄实景位置清单（拍摄位置实景），如果需要拍摄某级位置的实景照片则进行选择，不需要拍摄则不用选择，多值之间用,分隔 */
	shotZonePositions?: number | string;
	/** 拍照数量,每个点位需要拍摄照片的数量 */
	spotPhotoMax?: number;
	/** 点位仅允许拍照，是否仅允许拍照上传，即不允许从手机相册选择照片上传 */
	spotOnlyPhoto?: number;
	/** 实景最大数量，每个位置拍摄实景照片的最大数量 */
	zonePhotoMax?: number;
	/** 实景仅拍照 */
	zoneOnlyPhoto?: number;
	/** 实景拍照要求，可不填写，最多可输入100个字符 */
	photoRequirements?: string;
	/** 数据状态 */
	status?: number;
	/** True 为不能删，False为可以删除, 是否为保留数据 */
	reserved?: number;
	/** 版本号 */
	reversion?: number;
	/** 备注 */
	description?: string;
	/** 数据创建时间 */
	createTime?: Date;
	/** 数据更新时间 */
	updateTime?: Date;
	/** 创建人 */
	createBy?: string;
	/** 最后修改 */
	updateBy?: string;
	/** 排序值 */
	ranking?: number;
	
	/** 上画图片 */
	currentContentImageUrl?: string;
}
