import { TaskImages } from "./TaskImages";

/** OMaps —— Task, 上刊任务信息 */
export interface Task {
	/** 任务Id */
	taskId ?: string;
	/** 关联订单 */
	orderId ?: number | string;
	/** 关联发布 */
	itemId ?: number | string;
	/** 关联派单 */
	deliveryId ?: number | string;
	/** 所属小区 */
	zoneId ?: number;
	/** 关联点位 */
	spotId ?: number;
	/** 是否小区实景任务（实景任务） */
	zoneTask ?: number;
	/** 上刊开始日期 */
	beginDay ?: string;
	/** 上刊结束日期 */
	endDay ?: string;
	/** 实景位置类型 */
	zonePhotoType ?: number;
	/** 实景位置 */
	zonePhotoAddress ?: string;
	/** 拍照数量要求，每个任务需要拍摄照片的数量 */
	photoMax ?: number;
	/** 拍照要求说明，可不填写，最多可输入100个字符 */
	photoRequirements ?: string;
	/** 仅允许拍照，是否仅允许拍照上传，即不允许从手机相册选择照片上传 */
	onlyPhoto ?: number;
	/** 所属城市 */
	belongCity ?: string;
	/** 所属区 */
	belongDistrict ?: string;
	/** 位置描述/详细地址 */
	zoneAddress ?: string;
	/** 项目名称/楼盘名称 */
	zoneName ?: string;
	/** 点位编号 */
	spotCode ?: string;
	/** 广告楼层 */
	floorNo ?: string;
	/** 单元号 */
	apartNo ?: string;
	/** 电梯号 */
	elevatorNo ?: string;
	/** 广告位置 */
	position ?: string;
	/** 点位规格 */
	size ?: string;
	/** 点位状态 */
	spotStatus ?: number;
	/** 任务状态 */
	taskStatus ?: number | string;
	/** 任务创建时间 */
	createTime ?: Date;
	/** 任务创建人 */
	createBy ?: string;
	/** 任务分配时间 */
	assignTime ?: Date;
	/** 任务分配人 */
	assignBy ?: string;
	/** 任务接收时间 */
	receiveTime ?: Date;
	/** 任务接收人 */
	receiveBy ?: string;
	/** 任务完成时间 */
	completeTime ?: Date;
	/** 任务完成人 */
	completeBy ?: string;
	/** 任务取消时间 */
	cancelTime ?: Date;
	/** 任务取消人 */
	cancelBy ?: string;
	/** 数据状态 */
	status ?: number;
	/** True 为不能删，False为可以删除, 是否为保留数据 */
	reserved ?: number;
	/** 版本号 */
	reversion ?: number;
	/** 备注 */
	description ?: string;
	/** 数据创建时间 */
	/** 数据更新时间 */
	updateTime ?: Date;
	/** 创建人 */
	/** 最后修改 */
	updateBy ?: string;
	/** 排序值 */
	ranking ?: number;

	/** 点位最后一次上画内容图片 */
	previousContentImageUrl ?: string;

	/** 本次上画内容图片 */
	currentContentImageUrl ?: string;

	taskImages ?: TaskImages[];

	/** 折叠相同位置的其他任务 */
	collapsedZoneTasks ?: Task[];
}