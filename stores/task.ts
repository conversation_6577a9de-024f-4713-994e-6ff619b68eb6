import { Store, _UnwrapAll, defineStore } from 'pinia';
import { Ref, ref, computed, reactive, readonly } from 'vue';
import type { Delivery, Task } from '@/types';
import { showToast } from '@/utils';
import { TaskApi } from '@/utils/api';
import { useDeliveryStore } from './delivery';
import { useImagesStore } from './images';
import { useSettingsStore } from './settings';
import { useAuthStore } from './auth';
import { smartDataManager } from '@/utils/SmartDataManager';
import { performanceMonitor } from '@/utils/PerformanceMonitor';
import type { TaskStatusUpdate } from '@/utils/SmartStateManager';

// 核心业务数据接口（轻量响应式）
interface TaskCore {
	id: string;
	deliveryId: string;
	type: 'spot' | 'zone';
	code: string; // spotCode 或 zoneName
	district: string; // belongDistrict
	photoMax: number;
	completed: boolean;
	imageCount: number;
	lastUpdate: number;
}

// 高性能数据存储类（非响应式）
class TaskDataStorage {
	private rawTasks: Task[] = [];
	private taskMap = new Map<string, Task>();
	private tasksByDeliveryMap = new Map<string, Task[]>();

	// 设置任务数据
	setTasks(tasks: Task[]) {
		this.rawTasks = tasks;
		this.rebuildIndexes();
	}

	// 重建索引
	private rebuildIndexes() {
		this.taskMap.clear();
		this.tasksByDeliveryMap.clear();

		this.rawTasks.forEach((task) => {
			// 任务ID索引
			this.taskMap.set(task.taskId, task);

			// 派单ID索引
			if (!this.tasksByDeliveryMap.has(task.deliveryId)) {
				this.tasksByDeliveryMap.set(task.deliveryId, []);
			}
			this.tasksByDeliveryMap.get(task.deliveryId)!.push(task);
		});
	}

	// 获取任务（O(1)）
	getTask(taskId: string): Task | undefined {
		return this.taskMap.get(taskId);
	}

	// 获取派单的任务列表（O(1)）
	getTasksByDelivery(deliveryId: string): Task[] {
		return this.tasksByDeliveryMap.get(deliveryId) || [];
	}

	// 获取所有任务
	getAllTasks(): Task[] {
		return this.rawTasks;
	}

	// 更新任务
	updateTask(taskId: string, updates: Partial<Task>): boolean {
		const task = this.taskMap.get(taskId);
		if (task) {
			Object.assign(task, updates);
			return true;
		}
		return false;
	}

	// 添加任务
	addTask(task: Task) {
		this.rawTasks.push(task);
		this.taskMap.set(task.taskId, task);

		if (!this.tasksByDeliveryMap.has(task.deliveryId)) {
			this.tasksByDeliveryMap.set(task.deliveryId, []);
		}
		this.tasksByDeliveryMap.get(task.deliveryId)!.push(task);
	}
}

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 全局变量用于跟踪位置服务状态
let locationAvailable = true;

export const useTaskStore = defineStore('task', () => {
	// ===== 性能优化层 =====
	// 高性能数据存储（非响应式）
	const dataStorage = new TaskDataStorage();

	// 核心业务数据（轻量响应式）
	const taskCores = ref<TaskCore[]>([]);

	// 统计信息（响应式）
	const stats = reactive({
		total: 0,
		spot: { total: 0, completed: 0 },
		zone: { total: 0, completed: 0 },
		filtered: 0,
		byDelivery: new Map<string, { total: number; completed: number }>(),
	});

	// 更新优化层数据
	const updateOptimizedData = (taskList: Task[]) => {
		console.info('🚀 [TASK OPTIMIZATION] 开始更新优化层数据');
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		// 1. 存储原始数据到非响应式存储
		dataStorage.setTasks(taskList);

		// 2. 提取核心业务数据（轻量响应式）
		const imagesStore = useImagesStore();
		taskCores.value = taskList.map((task) => {
			const taskStats = imagesStore.getTaskStats(task.taskId, task.photoMax || 0);
			return {
				id: task.taskId,
				deliveryId: task.deliveryId,
				type: task.spotId === null ? 'zone' : 'spot',
				code: task.spotId === null ? task.zoneName || '' : task.spotCode || '',
				district: task.belongDistrict || '',
				photoMax: task.photoMax || 0,
				completed: task.taskStatus === 'COMPLETED',
				imageCount: taskStats.hasTaken + taskStats.pendingUpload,
				lastUpdate: Date.now(),
			};
		});

		// 3. 更新统计信息
		const spotTasks = taskList.filter((t) => t.spotId !== null);
		const zoneTasks = taskList.filter((t) => t.spotId === null);

		stats.total = taskList.length;
		stats.spot.total = spotTasks.length;
		stats.spot.completed = spotTasks.filter((t) => t.taskStatus === 'COMPLETED').length;
		stats.zone.total = zoneTasks.length;
		stats.zone.completed = zoneTasks.filter((t) => t.taskStatus === 'COMPLETED').length;

		// 4. 按派单统计
		stats.byDelivery.clear();
		taskList.forEach((task) => {
			const deliveryId = task.deliveryId;
			if (!stats.byDelivery.has(deliveryId)) {
				stats.byDelivery.set(deliveryId, { total: 0, completed: 0 });
			}
			const deliveryStats = stats.byDelivery.get(deliveryId)!;
			deliveryStats.total++;
			if (task.taskStatus === 'COMPLETED') {
				deliveryStats.completed++;
			}
		});

		const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
		console.info(`🚀 [TASK OPTIMIZATION] 优化层数据更新完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
		console.info(`📊 [TASK STATS] 总任务: ${stats.total}, 点位: ${stats.spot.total}, 实景: ${stats.zone.total}`);
	};

	// ===== 兼容层（保持现有API） =====
	// 第一层：所有任务列表（全集）
	const tasks = ref<Task[]>([]);

	// 优化的数据结构索引
	const tasksMap = computed(() => {
		const map = new Map<string | number, Task>();
		tasks.value.forEach((task: Task) => map.set(task.taskId, task));
		return map;
	});

	const tasksByDeliveryId = computed(() => {
		const map = new Map<string, Task[]>();
		tasks.value.forEach((task: Task) => {
			if (!map.has(task.deliveryId)) {
				map.set(task.deliveryId, []);
			}
			map.get(task.deliveryId)!.push(task);
		});
		return map;
	});

	// 选中的派单ID列表
	const selectedDeliveryIds = ref<string[]>([]);

	// 筛选条件
	const filters = ref({
		district: '',
		zone: 0,
		taskType: 'all',
		photoStatus: '',
		completionStatus: '',
		searchText: '',
	});

	// 加载状态
	const loading = ref(false);
	// 刷新状态
	const refreshing = ref(false);
	// 是否已有数据
	const hasData = ref(false);
	// 本地缓存key
	const CACHE_KEY = 'task_cache';
	const SELECTED_IDS_KEY = 'task_selected_ids';
	const FILTERS_KEY = 'task_filters';

	// 缓存优化相关
	let saveTimer: any = null;
	const SAVE_DEBOUNCE_DELAY = 1000; // 1秒防抖延迟
	let isDirty = false; // 标记数据是否需要保存

	// 从本地存储加载状态
	const loadStateFromStorage = () => {
		try {
			// 加载选中的派单ID列表
			const savedSelectedIds = uni.getStorageSync(SELECTED_IDS_KEY);
			if (savedSelectedIds) {
				selectedDeliveryIds.value = JSON.parse(savedSelectedIds);
			}

			// 加载筛选条件
			const savedFilters = uni.getStorageSync(FILTERS_KEY);
			if (savedFilters) {
				filters.value = JSON.parse(savedFilters);
			}
		} catch (error) {
			console.warn('加载状态失败:', error);
		}
	};

	// 保存状态到本地存储
	const saveStateToStorage = () => {
		try {
			// 保存选中的派单ID列表
			uni.setStorageSync(SELECTED_IDS_KEY, JSON.stringify(selectedDeliveryIds.value));

			// 保存筛选条件
			uni.setStorageSync(FILTERS_KEY, JSON.stringify(filters.value));
		} catch (error) {
			console.warn('保存状态失败:', error);
		}
	};

	// 合并实景任务
	const mergeZoneTasks = (taskList: Task[]) => {
		// 从设置中获取是否需要合并实景任务
		const settingsStore = useSettingsStore();
		const shouldMergeZoneTasks = settingsStore.settings.taskDisplaySettings.mergeZoneTasks;

		// 如果不需要合并，直接返回原始任务列表
		if (!shouldMergeZoneTasks) {
			return taskList;
		}

		// 创建结果数组和分组Map
		const result: Task[] = [];
		const zoneTaskGroups = new Map<string, Task>();

		// 遍历所有任务
		taskList.forEach((task) => {
			// 如果不是实景任务（spotId不为null），直接添加到结果中
			if (task.spotId !== null) {
				result.push(task);
				return;
			}

			// 对于实景任务，根据zoneId, zoneName, zonePhotoType, zonePhotoAddress分组
			const groupKey = `${task.zoneId}_${task.zoneName}_${task.zonePhotoType || ''}_${
				task.zonePhotoAddress || ''
			}`;

			if (!zoneTaskGroups.has(groupKey)) {
				// 如果是该组的第一个任务，创建一个新的主任务
				const mainTask = { ...task, collapsedZoneTasks: [] };
				zoneTaskGroups.set(groupKey, mainTask);
				result.push(mainTask);
			} else {
				// 如果该组已存在，将任务添加到主任务的collapsedZoneTasks中
				const mainTask = zoneTaskGroups.get(groupKey)!;
				if (mainTask.collapsedZoneTasks) {
					mainTask.collapsedZoneTasks.push(task);
				}
			}
		});

		return result;
	};

	// 应用筛选条件
	const applyFilters = (taskList: Task[]) => {
		return taskList.filter((task) => {
			// 区域筛选
			if (filters.value.district && task.belongDistrict !== filters.value.district) {
				return false;
			}

			// 小区筛选
			if (filters.value.zone && task.zoneId !== filters.value.zone) {
				return false;
			}

			// 任务类型筛选
			if (filters.value.taskType && filters.value.taskType !== 'all') {
				switch (filters.value.taskType) {
					case 'spot': // 仅点位任务
						if (task.spotId === null) return false;
						break;
					case 'zone': // 仅实景任务
						if (task.spotId !== null) return false;
						break;
				}
			}

			// 拍照状态筛选
			if (filters.value.photoStatus) {
				const imagesStore = useImagesStore();
				const photoStats = imagesStore.getTaskStats(task.taskId, task.photoMax || 0);
				const required = photoStats.shouldTake || 0;
				const hasTaken = photoStats.hasTaken || 0; // 已拍 = 服务端已确认的数量
				// const hasUploaded = photoStats.hasUploaded || 0; // 已传 = 客户端临时已传，客户端上传后，立刻更新了hasTaken
				const pendingUpload = photoStats.pendingUpload || 0; // 待传 = 客户端待传
				const totalTaken = hasTaken + pendingUpload; // 总拍摄数量

				switch (filters.value.photoStatus) {
					case 'notTaken': // 仅显示未拍照
						if (totalTaken > 0) return false;
						break;
					case 'taken': // 仅显示已拍照
						if (totalTaken === 0) return false;
						break;
					case 'notComplete': // 仅显示未拍照完成
						if (totalTaken === 0 || totalTaken >= required) return false;
						break;
					case 'complete': // 仅显示已拍照完成
						if (totalTaken < required || required === 0) return false;
						break;
				}
			}

			// 完成状态筛选
			if (filters.value.completionStatus) {
				switch (filters.value.completionStatus) {
					case 'notComplete': // 仅显示未完成
						if (task.taskStatus === 'COMPLETED') return false;
						break;
					case 'complete': // 仅显示已完成
						if (task.taskStatus !== 'COMPLETED') return false;
						break;
				}
			}

			// 搜索文本筛选
			if (filters.value.searchText) {
				const searchLower = filters.value.searchText.toLowerCase();
				// 搜索任务ID
				if (task.taskId && task.taskId.toString().toLowerCase().includes(searchLower)) return true;
				// 搜索小区名称
				if (task.zoneName && task.zoneName.toLowerCase().includes(searchLower)) return true;
				// 搜索地址
				if (task.zoneAddress && task.zoneAddress.toLowerCase().includes(searchLower)) return true;
				// 搜索区域
				if (task.belongDistrict && task.belongDistrict.toLowerCase().includes(searchLower)) return true;

				// 如果没有匹配项，则过滤掉
				return false;
			}

			// 通过所有筛选条件
			return true;
		});
	};

	// 获取任务的地址字符串，用于排序
	const getTaskAddressString = (task: Task) => {
		// 构建地址层级字符串：小区 > 楼层 > 单元 > 电梯 > 位置 > 点位
		const addressParts: string[] = [];

		// 小区（最高优先级，确保按小区分组）
		if (task.zoneName) addressParts.push(task.zoneName);

		// 参考 getShotLocation 函数的实现，构建详细地址部分
		const detailParts = [
			task?.floorNo ? task.floorNo + '层' : '',
			task?.apartNo ? task.apartNo + '单元' : '',
			task?.elevatorNo ? task.elevatorNo + '号梯' : '',
			task?.position || '',
		]
			.filter((part) => part && part.trim())
			.map((part) => part.replace('层层', '层'))
			.map((part) => part.replace('单元单元', '单元'))
			.map((part) => part.replace('号梯号梯', '号梯'));

		// 将详细地址部分添加到地址数组
		if (detailParts.length > 0) {
			addressParts.push(detailParts.join(' '));
		}

		// 点位ID（实景任务没有spotId，用空字符串表示，确保在同一地址下实景任务和点位任务可以区分）
		addressParts.push(task.spotId || '');

		return addressParts.join('_');
	};

	// 对任务进行排序
	const sortTasks = (taskList: Task[]) => {
		// 获取设置 store
		const settingsStore = useSettingsStore();
		const taskSortMode = settingsStore.settings.taskDisplaySettings.taskSortMode;

		// 根据任务排列方式进行排序
		switch (taskSortMode) {
			case 'mixed':
				// 按地址从大到小混排（zoneName已经在地址字符串的开头，所以会先按小区分组）
				return taskList.sort((a, b) => {
					// 首先按小区名称排序
					const aZone = a.zoneName || '';
					const bZone = b.zoneName || '';
					const zoneCompare = aZone.localeCompare(bZone);
					if (zoneCompare !== 0) return zoneCompare;

					// 然后按照完整地址排序
					return getTaskAddressString(a).localeCompare(getTaskAddressString(b));
				});

			case 'zoneFirst':
				// 按地址从大到小先实景再点位
				return taskList.sort((a, b) => {
					// 首先按小区名称排序
					const aZone = a.zoneName || '';
					const bZone = b.zoneName || '';
					const zoneCompare = aZone.localeCompare(bZone);
					if (zoneCompare !== 0) return zoneCompare;

					// 如果小区相同，一个是实景任务，一个是点位任务，实景任务排在前面
					if (a.spotId === null && b.spotId !== null) return -1;
					if (a.spotId !== null && b.spotId === null) return 1;

					// 否则按地址排序
					return getTaskAddressString(a).localeCompare(getTaskAddressString(b));
				});

			case 'spotFirst':
				// 按地址从大到小先点位再实景
				return taskList.sort((a, b) => {
					// 首先按小区名称排序
					const aZone = a.zoneName || '';
					const bZone = b.zoneName || '';
					const zoneCompare = aZone.localeCompare(bZone);
					if (zoneCompare !== 0) return zoneCompare;

					// 如果小区相同，一个是实景任务，一个是点位任务，点位任务排在前面
					if (a.spotId === null && b.spotId !== null) return 1;
					if (a.spotId !== null && b.spotId === null) return -1;

					// 否则按地址排序
					return getTaskAddressString(a).localeCompare(getTaskAddressString(b));
				});

			default:
				return taskList;
		}
	};

	// 第二层：基于 deliveryIds 过滤后的任务列表（优化版本，使用索引）
	const currentTasks = computed(() => {
		if (selectedDeliveryIds.value.length === 0) {
			// 如果没有选中的派单ID，当前任务列表为空
			return [];
		}

		// 使用索引快速获取任务，避免全量遍历
		let deliveryFilteredTasks: Task[] = [];
		const deliveryMap = tasksByDeliveryId.value;

		selectedDeliveryIds.value.forEach((deliveryId) => {
			const tasksForDelivery = deliveryMap.get(deliveryId);
			if (tasksForDelivery) {
				deliveryFilteredTasks.push(...tasksForDelivery);
			}
		});

		// 合并实景任务
		let processedTasks = mergeZoneTasks(deliveryFilteredTasks);

		// 对任务进行排序
		processedTasks = sortTasks(processedTasks);

		return processedTasks;
	});

	// 第三层：基于查询条件过滤的任务列表（自动计算）
	const filteredTasks = computed(() => {
		// 应用筛选条件到 currentTasks
		return applyFilters(currentTasks.value);
	});

	// 从缓存加载数据
	const loadFromCache = () => {
		try {
			const cached = uni.getStorageSync(CACHE_KEY);
			if (cached) {
				const cachedTasks = JSON.parse(cached);
				// 确保每个任务的 taskImages 属性都是响应式的数组
				tasks.value = cachedTasks.map((task: any) => {
					// 强制初始化 taskImages 为空数组，确保响应式
					const initializedTask = {
						...task,
						taskImages: [], // 始终初始化为空数组
					};

					// 如果缓存中有 taskImages 数据，则添加到数组中
					if (task.taskImages && Array.isArray(task.taskImages) && task.taskImages.length > 0) {
						initializedTask.taskImages = [...task.taskImages];
					}

					return initializedTask;
				});

				console.info(
					`📋 [TASK] 从缓存加载任务完成，共 ${tasks.value.length} 个任务，所有任务都有 taskImages 数组`
				);
				hasData.value = true;
				return true;
			}
		} catch (error) {
			console.warn('读取缓存失败:', error);
		}
		return false;
	};

	// 立即保存数据到缓存（同步）
	const saveToCacheSync = () => {
		try {
			const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
			uni.setStorageSync(CACHE_KEY, JSON.stringify(tasks.value));
			const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
			console.info(`💾 [CACHE] 同步保存缓存完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
			isDirty = false;
		} catch (error) {
			console.warn('保存缓存失败:', error);
		}
	};

	// 防抖保存数据到缓存（异步）
	const saveToCache = () => {
		isDirty = true;

		// 清除之前的定时器
		if (saveTimer) {
			clearTimeout(saveTimer);
		}

		// 设置新的定时器
		saveTimer = setTimeout(() => {
			if (isDirty) {
				saveToCacheSync();
			}
		}, SAVE_DEBOUNCE_DELAY);

		console.info(`💾 [CACHE] 已标记需要保存，将在 ${SAVE_DEBOUNCE_DELAY}ms 后执行`);
	};

	// 强制立即保存缓存
	const flushCache = () => {
		if (saveTimer) {
			clearTimeout(saveTimer);
			saveTimer = null;
		}
		if (isDirty) {
			saveToCacheSync();
		}
	};

	// 清除缓存数据（保留用户选择状态）
	const clearCache = () => {
		try {
			// 清除定时器
			if (saveTimer) {
				clearTimeout(saveTimer);
				saveTimer = null;
			}
			isDirty = false;

			// ✅ 清除任务数据缓存（服务端数据，可重新加载）
			uni.removeStorageSync(CACHE_KEY);

			// ❌ 保留用户选择的派单ID（用户工作状态）
			// uni.removeStorageSync(SELECTED_IDS_KEY);

			// ❌ 保留筛选条件（用户偏好设置）
			// uni.removeStorageSync(FILTERS_KEY);

			// ✅ 清除任务数据（服务端数据，可重新加载）
			tasks.value = [];

			// ❌ 保留用户选择的派单ID（用户工作状态）
			// selectedDeliveryIds.value = [];

			// ❌ 保留筛选条件（用户偏好设置）
			// filters.value = { ... };

			// ✅ 重置加载状态
			hasData.value = false;
			loading.value = false;
			refreshing.value = false;

			console.info('🗑️ [TASK CACHE] 已清除任务数据缓存（保留用户状态）');
		} catch (error) {
			console.warn('清除任务缓存失败:', error);
		}
	};

	// 获取delivery的统计数据，支持单个deliveryId或deliveryIds数组（优化版本，使用索引）
	const getDeliveryState = (deliveryIds: string | number | (string | number)[]) => {
		// 统一转换为数组
		const ids = Array.isArray(deliveryIds) ? deliveryIds : [deliveryIds];

		// 使用索引快速获取任务，避免全量遍历
		let deliveryTasks: Task[] = [];
		const deliveryMap = tasksByDeliveryId.value;

		ids.forEach((id) => {
			const tasksForDelivery = deliveryMap.get(String(id));
			if (tasksForDelivery) {
				deliveryTasks.push(...tasksForDelivery);
			}
		});

		// 只在没有找到任务时输出调试信息
		if (deliveryTasks.length === 0) {
			console.log(
				`📊 [GET_DELIVERY_STATE] 派单 ${ids.join(',')} 没有找到任务数据，索引Map大小: ${deliveryMap.size}`
			);
		}

		const zoneTasks = deliveryTasks.filter((task) => task.spotId === null);
		const spotTasks = deliveryTasks.filter((task) => task.spotId !== null);

		const zoneTotalCount = zoneTasks.length;
		const spotTotalCount = spotTasks.length;

		const zoneCompletedCount = zoneTasks.filter((task) => task.taskStatus === 'COMPLETED').length;
		const spotCompletedCount = spotTasks.filter((task) => task.taskStatus === 'COMPLETED').length;

		const zoneUncompleteCount = zoneTotalCount - zoneCompletedCount;
		const spotUncompleteCount = spotTotalCount - spotCompletedCount;

		// 计算拍照相关的统计数据
		const imagesStore = useImagesStore();
		let totalRequiredCount = 0;
		let totalUploadedCount = 0;
		let totalPendingCount = 0;

		deliveryTasks.forEach((task: Task) => {
			const photoStats = imagesStore.getTaskStats(task.taskId, task.photoMax || 0);
			totalRequiredCount += photoStats.shouldTake || 0; // 应拍
			totalUploadedCount += photoStats.hasTaken || 0; // 已传（服务端确认）
			totalPendingCount += photoStats.pendingUpload || 0; // 待传（本地待上传）
		});

		return {
			// 实景任务
			zoneTotalCount,
			zoneCompletedCount,
			zoneUncompleteCount,

			// 点位任务
			spotTotalCount,
			spotCompletedCount,
			spotUncompleteCount,

			// 拍照统计（用于派单完成状态判断）
			requiredCount: totalRequiredCount, // 应拍总数
			uploadedCount: totalUploadedCount, // 已传总数
			pendingCount: totalPendingCount, // 待传总数
		};
	};

	// 加载任务列表（集成智能数据管理系统）
	const loadTasks = async (forceLoad = false) => {
		console.info('📋 [TASK] 开始加载任务列表');

		// 先从本地存储加载状态
		if (!forceLoad) loadStateFromStorage();

		// 获取当前用户
		const authStore = useAuthStore();
		const username = authStore.getUsername();

		// 验证用户名是否有效
		if (!username || username.trim() === '') {
			console.error('❌ [TASK] 用户名为空，无法加载任务');
			showToast('用户信息无效，请重新登录', 'none');
			return;
		}

		try {
			// 使用智能数据管理系统检查并加载数据
			const needsLoad = await smartDataManager.checkAndLoadData(
				username,
				async () => {
					// 加载派单数据的回调
					const deliveryStore = useDeliveryStore();
					await deliveryStore.loadDeliveries(forceLoad);
				},
				async () => {
					// 加载任务数据的回调
					await performanceMonitor.measure(
						'task-data-load',
						'data-loading',
						async () => {
							await loadTasksFromAPI(username);
						},
						{ username, forceLoad }
					);
				},
				(stage: string, progress: number) => {
					// 进度回调
					console.info(`📋 [TASK] ${stage} - ${progress}%`);
				}
			);

			if (needsLoad) {
				// 执行数据分析
				const deliveryStore = useDeliveryStore();
				await smartDataManager.performDataAnalysis(tasks.value, deliveryStore.deliveries);

				console.info('✅ [TASK] 任务数据加载和分析完成');
			} else {
				console.info('✅ [TASK] 使用缓存的任务数据');
			}
		} catch (error) {
			console.error('❌ [TASK] 加载任务失败:', error);
			showToast('加载任务失败，请稍后重试', 'none');
		}
	};

	// 从API加载任务数据的内部函数
	const loadTasksFromAPI = async (username: string) => {
		if (!refreshing.value) {
			loading.value = true;
		}

		try {
			// 获取派单ID列表
			const deliveryStore = useDeliveryStore();
			// 使用 deliveryDataStorage 获取所有派单
			const deliveries = deliveryStore.getDeliveryCores();
			const deliveryIds = deliveries.map((delivery: any) => delivery.id);

			console.info(`📋 [TASK] 从API加载任务: 用户=${username}, 派单数=${deliveryIds.length}`);

			const response = await TaskApi.loadUserTasks(username, deliveryIds);

			// 确保每个任务的 taskImages 属性都是响应式的数组
			const tasksData = response?.data || [];
			tasks.value = tasksData.map((task: any) => {
				// 强制初始化 taskImages 为空数组，确保响应式
				const initializedTask = {
					...task,
					taskImages: [], // 始终初始化为空数组
				};

				// 如果服务端有 taskImages 数据，则添加到数组中
				if (task.taskImages && Array.isArray(task.taskImages) && task.taskImages.length > 0) {
					initializedTask.taskImages = [...task.taskImages];
				}

				return initializedTask;
			});

			// ===== 性能优化：同时更新优化层数据 =====
			updateOptimizedData(tasks.value);

			console.info(`📋 [TASK] 任务初始化完成，共 ${tasks.value.length} 个任务，所有任务都有 taskImages 数组`);

			hasData.value = true;
			saveToCacheSync(); // 加载完成后立即保存

			console.info('📋加载了「' + tasks.value.length + '」条任务');
		} finally {
			loading.value = false;
		}
	};

	// 刷新任务列表
	const refreshTasks = async () => {
		try {
			refreshing.value = true;
			await loadTasks(true);
		} finally {
			refreshing.value = false;
		}
	};

	// 加载单个或多个任务数据
	const loadTask = async (taskIds: string | number | (string | number)[]) => {
		try {
			// 将单个任务ID转换为数组
			const ids = Array.isArray(taskIds) ? taskIds : [taskIds];

			// 批量请求任务详情
			const response = await TaskApi.loadTasksDetail(ids);

			if (response?.data && Array.isArray(response.data)) {
				// 更新任务列表中的数据
				let taskLists = response.data as Task[];
				taskLists.forEach((taskData) => {
					const taskIndex = tasks.value.findIndex((t) => t.taskId === taskData.taskId);
					if (taskIndex !== -1) {
						// 强制初始化 taskImages 为空数组，确保响应式
						const initializedTask = {
							...taskData,
							taskImages: [], // 始终初始化为空数组
						};

						// 如果服务端有 taskImages 数据，则添加到数组中
						if (
							taskData.taskImages &&
							Array.isArray(taskData.taskImages) &&
							taskData.taskImages.length > 0
						) {
							initializedTask.taskImages = [...taskData.taskImages];
						}

						tasks.value[taskIndex] = initializedTask;
					}
				});
				// 更新缓存
				saveToCache();
				// currentTasks 和 filteredTasks 会自动重新计算（computed）
			}
		} catch (error) {
			console.error('获取任务数据失败:', error);
		}
	};

	// 获取任务对应的派单名称
	const getTaskDeliveryName = (task: Task) => {
		const deliveryStore = useDeliveryStore();
		const { getDeliveryName, getFullDelivery } = deliveryStore;

		// 如果任务有折叠的实景任务，收集所有相关的派单
		if (task.collapsedZoneTasks && task.collapsedZoneTasks.length > 0) {
			// 收集所有相关的派单ID
			const allTasks = [task, ...task.collapsedZoneTasks];
			const deliveryIds = [...new Set(allTasks.map((t) => t.deliveryId))];

			// 如果只有一个派单ID，直接返回派单名称
			if (deliveryIds.length === 1) {
				const delivery = getFullDelivery(deliveryIds[0]);
				return getDeliveryName(delivery);
			}

			// 如果有多个派单ID，返回派单名称数组
			const deliveryNames = deliveryIds.map((id) => {
				const delivery = getFullDelivery(id);
				return getDeliveryName(delivery);
			});

			// 返回派单名称数组，让视图层决定如何显示
			return deliveryNames;
		}

		// 如果任务没有折叠的实景任务，直接返回派单名称
		const delivery = getFullDelivery(task.deliveryId);
		return getDeliveryName(delivery);
	};

	// 根据任务类型获取编号或小区名称
	const getTaskDeliveryNameById = (taskId: string | number) => {
		const task = tasks.value.find((t) => t.taskId === taskId);
		if (!task) return '未关联';
		return getTaskDeliveryName(task);
	};

	// 获取任务的所有图片（包括本地和已上传的，考虑折叠任务）
	// const getTaskImages = (taskId: string | number) => {
	// 	const imagesStore = useImagesStore();
	// 	const task = tasks.value.find((t) => t.taskId === taskId);

	// 	// 如果是主任务（有折叠任务），需要考虑所有折叠任务的图片
	// 	if (task && task.collapsedZoneTasks && task.collapsedZoneTasks.length > 0) {
	// 		// 收集所有相关任务ID
	// 		const allTaskIds = [task.taskId, ...task.collapsedZoneTasks.map((t) => t.taskId)];

	// 		// 收集所有任务的图片
	// 		let allImages: any[] = [];
	// 		for (const id of allTaskIds) {
	// 			// 获取本地图片
	// 			const localImages = imagesStore.getLocalTaskImages(id);
	// 			// 获取服务器图片
	// 			const taskWithImages = tasks.value.find((t) => t.taskId === id);
	// 			const serverImages = taskWithImages?.taskImages || [];
	// 			// 合并图片
	// 			allImages = [...allImages, ...localImages, ...serverImages];
	// 		}

	// 		return allImages;
	// 	}

	// 	// 如果是折叠任务，需要查找主任务
	// 	for (const mainTask of tasks.value) {
	// 		if (mainTask.collapsedZoneTasks && mainTask.collapsedZoneTasks.length > 0) {
	// 			const isCollapsed = mainTask.collapsedZoneTasks.some((t) => t.taskId === taskId);
	// 			if (isCollapsed) {
	// 				// 使用主任务的图片
	// 				return getTaskImages(mainTask.taskId);
	// 			}
	// 		}
	// 	}

	// 	// 如果是普通任务，直接获取
	// 	const localTaskImages = imagesStore.getLocalTaskImages(taskId);
	// 	const serverImages = task?.taskImages || [];
	// 	return [...localTaskImages, ...serverImages];
	// };

	// 计算完整地址
	const getShotLocation = (task: Task): string => {
		const addressParts = [
			task?.floorNo ? task.floorNo + '层' : '',
			task?.apartNo ? task.apartNo + '单元' : '',
			task?.elevatorNo ? task.elevatorNo + '号梯' : '',
			task?.position || '',
		]
			.filter((part) => part && part.trim())
			.map((part) => part.replace('层层', '层'))
			.map((part) => part.replace('单元单元', '单元'))
			.map((part) => part.replace('号梯号梯', '号梯'));
		return addressParts.join(' ') || '小区大门';
	};

	// 设置位置服务状态
	const setLocationAvailable = (available: boolean) => {
		locationAvailable = available;
	};

	// 获取位置服务状态
	const isLocationAvailable = () => locationAvailable;

	// 设置选中的派单ID列表
	const setSelectedDeliveryIds = (ids: string | string[]) => {
		// 如果传入的是字符串，转换为数组
		selectedDeliveryIds.value = Array.isArray(ids) ? ids : [ids];
		// currentTasks 和 filteredTasks 会自动重新计算（computed）
		// 保存到本地存储
		saveStateToStorage();
	};

	// 获取选中的派单ID列表
	const getSelectedDeliveryIds = () => selectedDeliveryIds;

	// 更新任务的 taskImages 数组（集成智能状态管理）
	const updateTaskImages = async (taskId: string | number, newImage: any) => {
		const taskIndex = tasks.value.findIndex((t: Task) => t.taskId === taskId);
		if (taskIndex !== -1) {
			const currentTask = tasks.value[taskIndex];
			console.log(`📋 [TASK] 更新任务 ${taskId} 的图片，当前图片数: ${currentTask.taskImages.length}`);

			// 创建新的 taskImages 数组，触发响应式更新
			const newTaskImages = [...currentTask.taskImages, newImage];

			// 创建新的任务对象
			const updatedTask = {
				...currentTask,
				taskImages: newTaskImages,
			};

			// 替换整个任务对象以确保响应式更新
			tasks.value.splice(taskIndex, 1, updatedTask);

			// 更新优化层数据
			updateOptimizedData(tasks.value);

			// 使用智能状态管理器通知变化
			await smartDataManager.updateTaskStatus(taskId.toString(), {
				taskId: taskId.toString(),
				deliveryId: currentTask.deliveryId,
				imageCount: newTaskImages.length,
				taskImages: newTaskImages,
				timestamp: Date.now(),
			});

			console.log(`📋 [TASK] 任务 ${taskId} 图片更新完成，新图片数: ${updatedTask.taskImages.length}`);

			// 保存到缓存
			saveToCache();

			return true;
		}
		console.warn(`📋 [TASK] 未找到任务 ${taskId}`);
		return false;
	};

	// 更新任务状态（集成智能状态管理）
	const updateTaskStatus = async (taskId: string | number, newStatus: string) => {
		const taskIndex = tasks.value.findIndex((t: Task) => t.taskId === taskId);
		if (taskIndex !== -1) {
			const currentTask = tasks.value[taskIndex];
			console.log(`📋 [TASK] 更新任务 ${taskId} 的状态: ${currentTask.taskStatus} → ${newStatus}`);

			// 创建新的任务对象
			const updatedTask = {
				...currentTask,
				taskStatus: newStatus,
			};

			// 替换整个任务对象以确保响应式更新
			tasks.value.splice(taskIndex, 1, updatedTask);

			// 更新优化层数据
			updateOptimizedData(tasks.value);

			// 使用智能状态管理器通知变化
			await smartDataManager.updateTaskStatus(taskId.toString(), {
				taskId: taskId.toString(),
				deliveryId: currentTask.deliveryId,
				completionStatus: newStatus as 'COMPLETED' | 'IN_PROGRESS',
				timestamp: Date.now(),
			});

			console.log(`📋 [TASK] 任务 ${taskId} 状态更新完成: ${newStatus}`);

			// 触发任务完成事件（防止循环调用）
			if (newStatus === 'COMPLETED') {
				// 使用 nextTick 避免同步调用造成的循环
				uni.$nextTick(() => {
					uni.$emit('taskCompleted', {
						taskId: taskId.toString(),
						deliveryId: currentTask.deliveryId,
						timestamp: Date.now(),
					});
				});
			}

			// 保存到缓存
			saveToCache();

			return true;
		}
		console.warn(`📋 [TASK] 未找到任务 ${taskId}`);
		return false;
	};

	// 设置筛选条件（只影响第三层 filteredTasks）
	const setFilters = (newFilters: Partial<typeof filters.value>) => {
		// 更新筛选条件
		Object.assign(filters.value, newFilters);
		// filteredTasks 会自动重新计算（computed）
		// 保存状态到本地存储
		saveStateToStorage();
	};

	// 优化的 API 方法
	const getFullTask = (taskId: string): Task | undefined => {
		return dataStorage.getTask(taskId);
	};

	const getTasksByDelivery = (deliveryId: string): Task[] => {
		return dataStorage.getTasksByDelivery(deliveryId);
	};

	const getOptimizedStats = () => readonly(stats);

	const getTaskCores = () => readonly(taskCores);

	return {
		// ===== 性能优化层 API =====
		taskCores: readonly(taskCores),
		stats: readonly(stats),
		getFullTask,
		getTasksByDelivery,
		getOptimizedStats,
		getTaskCores,

		// ===== 兼容层 API（保持现有接口） =====
		// 三层数据架构
		tasks, // 第一层：所有任务的全集
		currentTasks, // 第二层：基于 deliveryIds 过滤后的集合（包含合并和排序）
		filteredTasks, // 第三层：基于查询条件过滤的结果

		// 优化的数据结构索引
		tasksMap, // 任务ID到任务的映射
		tasksByDeliveryId, // 派单ID到任务列表的映射

		// 状态管理
		loading,
		refreshing,
		hasData,
		filters,
		selectedDeliveryIds,

		getDeliveryState,

		// 数据操作
		loadTask,
		loadTasks,
		refreshTasks,

		// 更新函数
		setFilters, // 设置筛选条件（只影响第三层）
		setSelectedDeliveryIds, // 设置派单ID（影响第二层和第三层）
		updateTaskImages, // 更新任务图片
		updateTaskStatus, // 更新任务状态

		// 工具函数
		getTaskDeliveryName,
		getTaskDeliveryNameById,
		// getTaskImages,
		getShotLocation,
		setLocationAvailable,
		isLocationAvailable,
		getSelectedDeliveryIds,
		loadStateFromStorage,
		saveStateToStorage,

		// 缓存管理
		flushCache,
		clearCache,
	};
});
