#!/usr/bin/env node

/**
 * 第一轮图标优化脚本
 * 
 * 功能：
 * 1. 分析Vue文件中实际使用的图标
 * 2. 生成需要的图标列表
 * 3. 创建优化建议
 */

const fs = require('fs');
const path = require('path');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');
const CSS_FILE = path.join(ROOT_DIR, 'static', 'css', 'icons.css');
const ICONFONT_JSON = path.join(ROOT_DIR, 'static', 'fonts', 'iconfont.json');

// Vue文件列表
const VUE_FILES = [
  'pages/delivery/index.vue',
  'pages/delivery/components/delivery-navbar.vue',
  'pages/images/components/images-navbar.vue',
  'pages/my/index.vue',
  'components/custom-tabbar.vue',
  'pages/task/index.vue',
  'pages/task/task-detail.vue',
  'pages/task/components/task-navbar.vue'
];

console.log('🔍 第一轮图标优化分析');
console.log('================================');

// 扫描Vue文件中使用的图标
function scanUsedIcons() {
  const usedIcons = new Map(); // 使用Map来记录使用位置
  
  VUE_FILES.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 查找 icon-xxx 类名
      const iconRegex = /icon-([a-zA-Z-]+)/g;
      let match;
      const fileIcons = new Set();
      
      while ((match = iconRegex.exec(content)) !== null) {
        const iconName = match[1];
        fileIcons.add(iconName);
        
        if (!usedIcons.has(iconName)) {
          usedIcons.set(iconName, []);
        }
        usedIcons.get(iconName).push(filePath);
      }
      
      console.log(`📄 ${filePath}: ${fileIcons.size} 个图标`);
      fileIcons.forEach(icon => {
        console.log(`   - icon-${icon}`);
      });
    }
  });
  
  return usedIcons;
}

// 读取iconfont配置
function readIconFontConfig() {
  if (!fs.existsSync(ICONFONT_JSON)) {
    console.log('❌ iconfont.json 文件不存在');
    return null;
  }
  
  try {
    const content = fs.readFileSync(ICONFONT_JSON, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.log('❌ 读取 iconfont.json 失败:', error.message);
    return null;
  }
}

// 读取当前CSS中的图标定义
function readCurrentIcons() {
  const content = fs.readFileSync(CSS_FILE, 'utf8');
  const iconRegex = /\.icon-([^:]+)::before\s*{\s*content:\s*"\\([^"]+)"/g;
  const icons = {};
  
  let match;
  while ((match = iconRegex.exec(content)) !== null) {
    const iconName = match[1];
    const unicode = match[2];
    icons[iconName] = unicode;
  }
  
  return icons;
}

// 分析图标使用情况
function analyzeIconUsage() {
  const usedIcons = scanUsedIcons();
  const currentIcons = readCurrentIcons();
  const iconFontConfig = readIconFontConfig();
  
  console.log('\n📊 图标使用分析');
  console.log('================================');
  console.log(`实际使用的图标: ${usedIcons.size}`);
  console.log(`CSS中定义的图标: ${Object.keys(currentIcons).length}`);
  
  // 找出未使用的图标
  const unusedIcons = Object.keys(currentIcons).filter(icon => !usedIcons.has(icon));
  console.log(`未使用的图标: ${unusedIcons.length}`);
  
  if (unusedIcons.length > 0) {
    console.log('\n🗑️  未使用的图标（可以删除）:');
    unusedIcons.forEach(icon => {
      console.log(`   icon-${icon} (${currentIcons[icon]})`);
    });
  }
  
  // 找出缺少定义的图标
  const missingIcons = [...usedIcons.keys()].filter(icon => !currentIcons[icon]);
  if (missingIcons.length > 0) {
    console.log('\n❌ 缺少定义的图标:');
    missingIcons.forEach(icon => {
      console.log(`   icon-${icon} (使用位置: ${usedIcons.get(icon).join(', ')})`);
    });
  }
  
  // 生成需要的图标列表
  console.log('\n📋 需要的图标列表:');
  console.log('================================');
  
  const neededIcons = [...usedIcons.keys()].sort();
  neededIcons.forEach(icon => {
    const unicode = currentIcons[icon] || '未定义';
    const files = usedIcons.get(icon);
    console.log(`icon-${icon.padEnd(15)} : ${unicode.padEnd(8)} (${files.length} 个文件)`);
  });
  
  // 生成iconfont项目需要的图标关键词
  if (iconFontConfig) {
    console.log('\n🎯 iconfont项目图标建议:');
    console.log('================================');
    
    const iconKeywords = {
      'wifi': 'wifi 信号 网络',
      'location': '位置 定位 导航 地图',
      'search': '搜索 查找 放大镜',
      'arrow-left': '箭头 返回 左 back',
      'close': '关闭 删除 叉 cross',
      'menu': '菜单 更多 三横线 hamburger',
      'house': '房屋 家 住宅 home',
      'building': '建筑 大楼 办公 框架',
      'point': '点位 标记 位置',
      'clipboard': '剪贴板 文档 列表 派单',
      'camera': '相机 拍照 摄像',
      'upload': '上传 云 发送 纸飞机',
      'check': '勾选 对号 完成 确认',
      'settings': '设置 齿轮 配置',
      'refresh': '刷新 重新加载 更新',
      'image': '图片 照片 相册',
      'album': '相册 图库 照片',
      'clock': '时钟 时间 计时',
      'exchange': '交换 切换 移动网络',
      'calendar': '日历 日期 时间',
      'chart': '图表 统计 数据',
      'users': '用户组 团队 多人',
      'user': '用户 个人 头像',
      'confirm': '确认 对号 同意',
      'cancel': '取消 叉号 拒绝',
      'delivery': '派单 任务 工作',
      'task': '任务 工作 待办',
      'queue': '队列 消息 通知',
      'my': '我的 个人 用户',
      'history': '历史 记录 过去',
      'attendance': '考勤 出勤 统计',
      'customer': '客户 用户 联系人',
      'emoji': '表情 图标 符号',
      'back': '返回 后退 箭头',
      'more': '更多 菜单 选项',
      'navigation': '导航 指南针 方向'
    };
    
    neededIcons.forEach(icon => {
      const keywords = iconKeywords[icon] || icon;
      console.log(`${icon.padEnd(15)} : ${keywords}`);
    });
  }
  
  return {
    usedIcons: [...usedIcons.keys()],
    unusedIcons,
    missingIcons,
    neededIcons
  };
}

// 主函数
function main() {
  const analysis = analyzeIconUsage();
  
  console.log('\n✅ 第一轮分析完成');
  console.log(`需要保留的图标: ${analysis.neededIcons.length}`);
  console.log(`可以删除的图标: ${analysis.unusedIcons.length}`);
  console.log(`需要添加的图标: ${analysis.missingIcons.length}`);
  
  // 生成优化建议
  console.log('\n💡 优化建议:');
  console.log('1. 从iconfont.cn下载包含上述关键词的图标');
  console.log('2. 确保图标风格一致（线条粗细、大小、风格）');
  console.log('3. 删除未使用的图标定义以减小文件大小');
  console.log('4. 统一各区域图标大小设置');
}

// 运行脚本
main();
