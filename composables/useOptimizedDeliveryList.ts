import { computed, ref, readonly } from 'vue';
import { useDeliveryStore } from '@/stores/delivery';
import { useTaskStore } from '@/stores/task';
import { useSettingsStore } from '@/stores/settings';

/**
 * 优化的派单列表组合式函数
 * 提供高性能的派单数据访问和操作
 */
export const useOptimizedDeliveryList = () => {
	const deliveryStore = useDeliveryStore();
	const taskStore = useTaskStore();
	const settingsStore = useSettingsStore();

	// 分页状态
	const currentPage = ref(0);
	const pageSize = ref(50);

	// 获取优化的派单核心数据
	const deliveryCores = computed(() => deliveryStore.deliveryCores);

	// 获取统计信息
	const stats = computed(() => deliveryStore.stats);

	// 筛选后的派单核心数据
	const filteredDeliveryCores = computed(() => {
		let filtered = deliveryCores.value;
		const filterSettings = deliveryStore.filterSettings;

		// 应用完成状态筛选
		const completionStatus = settingsStore.settings.deliveryDisplaySettings?.completionStatus || 'incomplete';
		if (completionStatus !== 'all') {
			filtered = filtered.filter(delivery => {
				// 使用任务 store 获取准确的任务统计
				const deliveryStats = taskStore.getDeliveryState(delivery.id);
				const isComplete = deliveryStats.requiredCount > 0 &&
					(deliveryStats.uploadedCount + deliveryStats.pendingCount) >= deliveryStats.requiredCount;

				if (completionStatus === 'complete') {
					return isComplete; // 仅显示已完成
				} else if (completionStatus === 'incomplete') {
					return !isComplete; // 仅显示未完成
				}
				return true;
			});
		}

		// 应用排序
		const sorted = [...filtered].sort((a, b) => {
			const dateA = new Date(a.date).getTime();
			const dateB = new Date(b.date).getTime();
			return filterSettings.sortOrder === 'time_asc' ? dateA - dateB : dateB - dateA;
		});

		return sorted;
	});

	// 搜索筛选
	const searchFilteredDeliveryCores = computed(() => {
		return (searchText: string) => {
			if (!searchText) return filteredDeliveryCores.value;

			const searchLower = searchText.toLowerCase();
			return filteredDeliveryCores.value.filter(delivery => {
				// 搜索派单名称
				if (delivery.name.toLowerCase().includes(searchLower)) return true;
				// 搜索派单ID
				if (delivery.id.toLowerCase().includes(searchLower)) return true;
				// 搜索队列ID
				if (delivery.queueId.toLowerCase().includes(searchLower)) return true;
				return false;
			});
		};
	});

	// 合并处理
	const processedDeliveryCores = computed(() => {
		return (searchText: string = '') => {
			let processed = searchFilteredDeliveryCores.value(searchText);
			const filterSettings = deliveryStore.filterSettings;

			// 如果选择了手动合并，并且有手动合并的结果
			if (filterSettings.mergeType === 'manual_merge' && deliveryStore.manualMergedDelivery) {
				const manualMerged = deliveryStore.manualMergedDelivery;
				return [{
					id: manualMerged.deliveryId,
					name: manualMerged.deliveryName,
					date: manualMerged.deliveryDate ? new Date(manualMerged.deliveryDate).toISOString().split('T')[0] : 'unknown',
					queueId: manualMerged.queueId || 'unknown',
					photoRequirements: manualMerged.photoRequirements || '',
					isGrouped: true,
					mergedCount: manualMerged.mergedCount || 1,
					lastUpdate: Date.now()
				}];
			}

			// 根据合并类型进行处理
			if (filterSettings.mergeType === 'merge_today') {
				// 按日期合并
				const groupedByDate: Record<string, any> = {};

				processed.forEach(delivery => {
					const dateStr = delivery.date;
					if (!groupedByDate[dateStr]) {
						groupedByDate[dateStr] = {
							...delivery,
							isGrouped: true,
							mergedCount: 1,
							groupItems: [delivery]
						};
					} else {
						const group = groupedByDate[dateStr];
						group.mergedCount++;
						group.groupItems.push(delivery);
					}
				});

				return Object.values(groupedByDate);
			}

			// 不合并，直接返回
			return processed;
		};
	});

	// 分页后的派单数据
	const paginatedDeliveryCores = computed(() => {
		return (searchText: string = '') => {
			const processed = processedDeliveryCores.value(searchText);
			const start = 0;
			const end = (currentPage.value + 1) * pageSize.value;
			return processed.slice(start, end);
		};
	});

	// 是否还有更多数据
	const hasMore = computed(() => {
		return (searchText: string = '') => {
			const processed = processedDeliveryCores.value(searchText);
			return (currentPage.value + 1) * pageSize.value < processed.length;
		};
	});

	// 加载更多数据
	const loadMore = () => {
		currentPage.value++;
	};

	// 重置分页
	const resetPagination = () => {
		currentPage.value = 0;
	};

	// 获取完整派单数据（按需）
	const getFullDelivery = (deliveryId: string) => {
		return deliveryStore.getFullDelivery(deliveryId);
	};

	// 获取派单显示数据（用于UI渲染）
	const getDeliveryDisplayData = (deliveryCore: any) => {
		const fullDelivery = getFullDelivery(deliveryCore.id);
		const deliveryStats = taskStore.getDeliveryState(deliveryCore.id);

		return {
			// 核心数据（轻量）
			deliveryId: deliveryCore.id,
			name: deliveryCore.name,
			date: deliveryCore.date,
			queueId: deliveryCore.queueId,
			photoRequirements: deliveryCore.photoRequirements,
			isGrouped: deliveryCore.isGrouped,
			mergedCount: deliveryCore.mergedCount,

			// 统计数据
			stats: deliveryStats,

			// 完整数据（按需）
			fullData: fullDelivery,

			// 显示用的计算属性
			displayName: deliveryCore.name,
			statusText: deliveryStats.requiredCount > 0 &&
				(deliveryStats.uploadedCount + deliveryStats.pendingCount) >= deliveryStats.requiredCount
				? '已完成' : '进行中',
			progressText: `${deliveryStats.uploadedCount + deliveryStats.pendingCount}/${deliveryStats.requiredCount}`,
			isComplete: deliveryStats.requiredCount > 0 &&
				(deliveryStats.uploadedCount + deliveryStats.pendingCount) >= deliveryStats.requiredCount
		};
	};

	// 批量获取显示数据
	const getDisplayDataList = computed(() => {
		return (searchText: string = '') => {
			return paginatedDeliveryCores.value(searchText).map(deliveryCore =>
				getDeliveryDisplayData(deliveryCore)
			);
		};
	});

	// 更新派单状态（同时更新优化层）
	const updateDeliveryStatus = async (deliveryId: string, newStatus: string) => {
		// 更新优化层
		const deliveryCore = deliveryCores.value.find(d => d.id === deliveryId);
		if (deliveryCore) {
			deliveryCore.lastUpdate = Date.now();
		}

		return true;
	};

	// 主动更新派单统计
	const updateDeliveryStats = (deliveryIds?: string[]) => {
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		if (deliveryIds) {
			// 增量更新：只更新指定的派单
			deliveryIds.forEach(deliveryId => {
				const deliveryCore = deliveryCores.value.find(d => d.id === deliveryId);
				if (deliveryCore) {
					deliveryCore.lastUpdate = Date.now();
				}
			});
			console.info(`📊 [DELIVERY] 增量更新派单统计，更新派单数: ${deliveryIds.length}`);
		} else {
			// 全量更新：触发所有派单的统计重新计算
			deliveryCores.value.forEach(deliveryCore => {
				deliveryCore.lastUpdate = Date.now();
			});
			console.info(`📊 [DELIVERY] 全量更新派单统计，派单数: ${deliveryCores.value.length}`);
		}

		const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
		console.info(`📊 [DELIVERY] 派单统计更新完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
	};

	// 获取按日期分组的统计
	const getDateGroupStats = computed(() => {
		const dateStats = new Map<string, { total: number; completed: number; incomplete: number }>();

		filteredDeliveryCores.value.forEach(delivery => {
			const dateStr = delivery.date;
			if (!dateStats.has(dateStr)) {
				dateStats.set(dateStr, { total: 0, completed: 0, incomplete: 0 });
			}

			const stat = dateStats.get(dateStr)!;
			stat.total++;

			const deliveryStats = taskStore.getDeliveryState(delivery.id);
			const isComplete = deliveryStats.requiredCount > 0 &&
				(deliveryStats.uploadedCount + deliveryStats.pendingCount) >= deliveryStats.requiredCount;

			if (isComplete) {
				stat.completed++;
			} else {
				stat.incomplete++;
			}
		});

		return dateStats;
	});

	return {
		// 数据
		deliveryCores: readonly(deliveryCores),
		filteredDeliveryCores: readonly(filteredDeliveryCores),
		searchFilteredDeliveryCores,
		processedDeliveryCores,
		paginatedDeliveryCores,
		getDisplayDataList,
		stats: readonly(stats),
		getDateGroupStats,

		// 分页
		currentPage: readonly(currentPage),
		hasMore,
		loadMore,
		resetPagination,

		// 操作
		getFullDelivery,
		getDeliveryDisplayData,
		updateDeliveryStatus,
		updateDeliveryStats,

		// 工具
		pageSize: readonly(pageSize)
	};
};
