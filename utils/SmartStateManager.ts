import type { StatusChange } from './DataAnalysisEngine';

/**
 * 任务状态更新信息
 */
export interface TaskStatusUpdate {
	taskId: string;
	deliveryId: string;
	imageCount?: number;
	completionStatus?: 'COMPLETED' | 'IN_PROGRESS';
	uploadedImages?: number;
	pendingImages?: number;
	taskImages?: any[];
	timestamp: number;
}

/**
 * 受影响的数据范围
 */
export interface AffectedData {
	tasks: string[]; // 受影响的任务ID
	deliveries: string[]; // 受影响的派单ID
	statistics: string[]; // 需要重新计算的统计项
	cacheKeys: string[]; // 需要失效的缓存键
	uiComponents: string[]; // 需要更新的UI组件
}

/**
 * 状态变化订阅者
 */
export type StateChangeSubscriber = (changes: StatusChange[]) => void;

/**
 * 智能状态管理器
 * 负责管理任务状态的精确更新和变化传播
 */
export class SmartStateManager {
	private changeSubscribers: StateChangeSubscriber[] = [];
	private pendingUpdates: Map<string, TaskStatusUpdate> = new Map();
	private updateTimer: any = null;
	private readonly DEBOUNCE_DELAY = 100; // 100ms 防抖延迟

	/**
	 * 更新任务状态
	 */
	async updateTaskStatus(taskId: string, statusUpdate: Partial<TaskStatusUpdate>): Promise<void> {
		console.info(`🔄 [STATE] 更新任务状态: ${taskId}`, statusUpdate);

		// 1. 合并待处理的更新
		const existingUpdate = this.pendingUpdates.get(taskId);
		const mergedUpdate: TaskStatusUpdate = {
			taskId,
			deliveryId: statusUpdate.deliveryId || existingUpdate?.deliveryId || '',
			...existingUpdate,
			...statusUpdate,
			timestamp: Date.now()
		};

		this.pendingUpdates.set(taskId, mergedUpdate);

		// 2. 防抖处理，避免频繁更新
		if (this.updateTimer) {
			clearTimeout(this.updateTimer);
		}

		this.updateTimer = setTimeout(() => {
			this.processPendingUpdates();
		}, this.DEBOUNCE_DELAY);
	}

	/**
	 * 批量更新状态
	 */
	async batchUpdateStatus(updates: TaskStatusUpdate[]): Promise<void> {
		console.info(`🔄 [STATE] 批量更新状态，数量: ${updates.length}`);

		// 合并到待处理更新中
		updates.forEach(update => {
			this.pendingUpdates.set(update.taskId, update);
		});

		// 立即处理（批量更新不使用防抖）
		await this.processPendingUpdates();
	}

	/**
	 * 订阅状态变化
	 */
	subscribeToChanges(callback: StateChangeSubscriber): () => void {
		this.changeSubscribers.push(callback);
		console.info(`📡 [STATE] 新增状态变化订阅者，当前订阅者数量: ${this.changeSubscribers.length}`);

		// 返回取消订阅函数
		return () => {
			const index = this.changeSubscribers.indexOf(callback);
			if (index > -1) {
				this.changeSubscribers.splice(index, 1);
				console.info(`📡 [STATE] 移除状态变化订阅者，当前订阅者数量: ${this.changeSubscribers.length}`);
			}
		};
	}

	/**
	 * 获取受影响的数据范围
	 */
	getAffectedData(changes: StatusChange[]): AffectedData {
		const affected: AffectedData = {
			tasks: [],
			deliveries: [],
			statistics: [],
			cacheKeys: [],
			uiComponents: []
		};

		const taskIds = new Set<string>();
		const deliveryIds = new Set<string>();
		const statisticsSet = new Set<string>();
		const cacheKeysSet = new Set<string>();
		const uiComponentsSet = new Set<string>();

		changes.forEach(change => {
			// 收集受影响的任务和派单
			taskIds.add(change.taskId);
			deliveryIds.add(change.deliveryId);

			// 根据变化类型确定影响范围
			change.affectedAreas.forEach(area => {
				switch (area) {
					case 'task-list':
						uiComponentsSet.add('task-list');
						cacheKeysSet.add(`filtered_tasks_${change.deliveryId}`);
						break;
					case 'delivery-stats':
						statisticsSet.add('delivery-completion');
						statisticsSet.add('delivery-progress');
						uiComponentsSet.add('delivery-navbar');
						break;
					case 'completion-rate':
						statisticsSet.add('overall-completion');
						uiComponentsSet.add('progress-summary');
						break;
					case 'image-stats':
						statisticsSet.add('image-progress');
						uiComponentsSet.add('image-counter');
						break;
					case 'upload-progress':
						uiComponentsSet.add('upload-indicator');
						break;
				}
			});
		});

		affected.tasks = Array.from(taskIds);
		affected.deliveries = Array.from(deliveryIds);
		affected.statistics = Array.from(statisticsSet);
		affected.cacheKeys = Array.from(cacheKeysSet);
		affected.uiComponents = Array.from(uiComponentsSet);

		return affected;
	}

	/**
	 * 处理待处理的更新
	 */
	private async processPendingUpdates(): Promise<void> {
		if (this.pendingUpdates.size === 0) return;

		const updates = Array.from(this.pendingUpdates.values());
		this.pendingUpdates.clear();

		console.info(`🔄 [STATE] 处理 ${updates.length} 个待处理的状态更新`);
		const startTime = performance.now();

		try {
			// 1. 分析变化影响范围
			const changes = this.analyzeChanges(updates);

			// 2. 获取受影响的数据范围
			const affectedData = this.getAffectedData(changes);

			// 3. 记录变化详情
			this.logChanges(changes, affectedData);

			// 4. 通知所有订阅者
			this.notifySubscribers(changes);

			const processingTime = performance.now() - startTime;
			console.info(`✅ [STATE] 状态更新处理完成，耗时: ${processingTime.toFixed(2)}ms`);

		} catch (error) {
			console.error('❌ [STATE] 处理状态更新失败:', error);
		}
	}

	/**
	 * 分析变化影响
	 */
	private analyzeChanges(updates: TaskStatusUpdate[]): StatusChange[] {
		return updates.map(update => {
			const affectedAreas = this.calculateAffectedAreas(update);
			const changeType = this.determineChangeType(update);

			return {
				type: changeType,
				taskId: update.taskId,
				deliveryId: update.deliveryId,
				oldValue: this.getCurrentValue(update.taskId),
				newValue: update,
				affectedAreas,
				timestamp: update.timestamp
			};
		});
	}

	/**
	 * 计算受影响的区域
	 */
	private calculateAffectedAreas(update: TaskStatusUpdate): string[] {
		const areas = ['task-list']; // 任务列表总是受影响

		// 根据更新类型确定影响范围
		if (update.completionStatus) {
			areas.push('delivery-stats', 'completion-rate', 'progress-summary');
		}

		if (update.imageCount !== undefined || update.uploadedImages !== undefined || update.pendingImages !== undefined) {
			areas.push('image-stats', 'upload-progress');
		}

		if (update.taskImages && update.taskImages.length > 0) {
			areas.push('image-gallery', 'upload-indicator');
		}

		return areas;
	}

	/**
	 * 确定变化类型
	 */
	private determineChangeType(update: TaskStatusUpdate): StatusChange['type'] {
		if (update.completionStatus) {
			return 'COMPLETION';
		}

		if (update.taskImages && update.taskImages.length > 0) {
			return 'IMAGE_UPLOAD';
		}

		if (update.imageCount !== undefined || update.uploadedImages !== undefined) {
			return 'TASK_STATUS';
		}

		return 'TASK_UPDATE';
	}

	/**
	 * 获取当前值（用于对比）
	 */
	private getCurrentValue(taskId: string): any {
		// 这里应该从数据存储中获取当前值
		// 暂时返回 null，实际实现时需要注入数据访问接口
		return null;
	}

	/**
	 * 记录变化详情
	 */
	private logChanges(changes: StatusChange[], affectedData: AffectedData): void {
		console.info(`📊 [STATE] 变化分析结果:`);
		console.info(`  - 变化数量: ${changes.length}`);
		console.info(`  - 受影响任务: ${affectedData.tasks.length} 个`);
		console.info(`  - 受影响派单: ${affectedData.deliveries.length} 个`);
		console.info(`  - 需要更新的统计: ${affectedData.statistics.join(', ')}`);
		console.info(`  - 需要失效的缓存: ${affectedData.cacheKeys.length} 个`);
		console.info(`  - 需要更新的UI组件: ${affectedData.uiComponents.join(', ')}`);

		// 详细记录每个变化
		changes.forEach((change, index) => {
			console.info(`  变化 ${index + 1}: ${change.type} - 任务 ${change.taskId} (派单 ${change.deliveryId})`);
		});
	}

	/**
	 * 通知所有订阅者
	 */
	private notifySubscribers(changes: StatusChange[]): void {
		if (this.changeSubscribers.length === 0) {
			console.info('📡 [STATE] 没有订阅者，跳过通知');
			return;
		}

		console.info(`📡 [STATE] 通知 ${this.changeSubscribers.length} 个订阅者`);

		this.changeSubscribers.forEach((subscriber, index) => {
			try {
				subscriber(changes);
			} catch (error) {
				console.error(`❌ [STATE] 通知订阅者 ${index} 失败:`, error);
			}
		});
	}

	/**
	 * 清除待处理的更新
	 */
	clearPendingUpdates(): void {
		this.pendingUpdates.clear();
		if (this.updateTimer) {
			clearTimeout(this.updateTimer);
			this.updateTimer = null;
		}
		console.info('🗑️ [STATE] 已清除所有待处理的更新');
	}

	/**
	 * 获取状态管理器信息
	 */
	getManagerInfo(): {
		subscriberCount: number;
		pendingUpdateCount: number;
		isProcessing: boolean;
	} {
		return {
			subscriberCount: this.changeSubscribers.length,
			pendingUpdateCount: this.pendingUpdates.size,
			isProcessing: this.updateTimer !== null
		};
	}

	/**
	 * 强制处理待处理的更新（用于测试或紧急情况）
	 */
	async forceProcessPendingUpdates(): Promise<void> {
		if (this.updateTimer) {
			clearTimeout(this.updateTimer);
			this.updateTimer = null;
		}
		await this.processPendingUpdates();
	}
}

// 创建全局实例
export const smartStateManager = new SmartStateManager();
