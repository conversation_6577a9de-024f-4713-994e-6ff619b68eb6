<template>
	<view class="performance-test">
		<view class="header">
			<text class="title">智能数据管理系统性能测试</text>
			<text class="subtitle">测试智能数据管理系统的性能表现</text>
		</view>

		<view class="controls">
			<button @click="loadTestData" :disabled="isLoading">
				{{ isLoading ? '加载中...' : '加载测试数据' }}
			</button>
			<button @click="clearAllData" :disabled="isLoading">
				清除所有数据
			</button>
			<button @click="generateReport">
				生成性能报告
			</button>
		</view>

		<view class="stats">
			<view class="section-title">
				<text>数据管理状态</text>
			</view>
			<view class="stat-item">
				<text class="label">初始化状态:</text>
				<text class="value" :class="{ success: isInitialized }">
					{{ isInitialized ? '已初始化' : '未初始化' }}
				</text>
			</view>
			<view class="stat-item">
				<text class="label">加载状态:</text>
				<text class="value" :class="{ loading: isLoading }">
					{{ isLoading ? '加载中' : '空闲' }}
				</text>
			</view>
			<view class="stat-item">
				<text class="label">最后加载时间:</text>
				<text class="value">{{ formatTime(lastLoadTime) }}</text>
			</view>
			<view class="stat-item">
				<text class="label">最后加载用户:</text>
				<text class="value">{{ lastLoadUser || '无' }}</text>
			</view>
			<view class="stat-item">
				<text class="label">数据版本:</text>
				<text class="value">{{ dataVersion }}</text>
			</view>
		</view>

		<view class="stats">
			<view class="section-title">
				<text>性能统计</text>
			</view>
			<view class="stat-item">
				<text class="label">平均加载时间:</text>
				<text class="value">{{ averageLoadTime.toFixed(2) }}ms</text>
			</view>
			<view class="stat-item">
				<text class="label">缓存命中率:</text>
				<text class="value">{{ cacheHitRate.toFixed(1) }}%</text>
			</view>
			<view class="stat-item" v-if="memoryUsage">
				<text class="label">内存使用:</text>
				<text class="value">{{ memoryUsage.used }}MB / {{ memoryUsage.total }}MB ({{ memoryUsage.percentage }}%)</text>
			</view>
			<view class="stat-item" v-if="performanceStats">
				<text class="label">总监控次数:</text>
				<text class="value">{{ performanceStats.totalMetrics }}</text>
			</view>
			<view class="stat-item" v-if="cacheStats">
				<text class="label">缓存项数量:</text>
				<text class="value">{{ cacheStats.totalItems }}</text>
			</view>
		</view>

		<view class="task-list">
			<view class="list-header">
				<text>任务列表 (显示前50个)</text>
			</view>
			<scroll-view scroll-y class="scroll-container">
				<view v-for="task in displayTasks" :key="task.taskId || task.id" class="task-item">
					<view class="task-info">
						<text class="task-id">{{ task.taskId || task.id }}</text>
						<text class="task-name">{{ task.code || task.spotCode || task.zoneName }}</text>
						<text class="task-type">{{ task.type || (task.spotId ? '点位' : '实景') }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { useTaskStore } from '@/stores/task';
import { useDeliveryStore } from '@/stores/delivery';
import { useOptimizedTaskList } from '@/composables/useOptimizedTaskList';
import { useSmartDataManager } from '@/composables/useSmartDataManager';
import { storeToRefs } from 'pinia';

// Store 和优化层
const taskStore = useTaskStore();
const deliveryStore = useDeliveryStore();
const { tasks } = storeToRefs(taskStore);
const optimizedTaskList = useOptimizedTaskList();
const { taskCores } = optimizedTaskList;

// 智能数据管理系统
const smartDataManager = useSmartDataManager();
const {
	isInitialized,
	isLoading,
	lastLoadTime,
	lastLoadUser,
	dataVersion,
	averageLoadTime,
	cacheHitRate,
	memoryUsage,
	performanceStats,
	cacheStats,
	checkAndLoadData,
	forceLoadData,
	clearAllData: clearManagerData,
	generatePerformanceReport
} = smartDataManager;

// 显示的任务列表（使用优化的核心数据）
const displayTasks = computed(() => {
	// 直接使用优化的核心数据
	return taskCores.value.slice(0, 50);
});

// 加载测试数据
const loadTestData = async () => {
	console.info('📊 [PERFORMANCE TEST] 开始加载测试数据');

	try {
		// 使用智能数据管理系统加载数据
		await forceLoadData(
			'test_user',
			async () => {
				// 加载派单数据
				await deliveryStore.loadDeliveries(true);
			},
			async () => {
				// 加载任务数据
				await taskStore.loadTasks(true);
			},
			(stage: string, progress: number) => {
				console.info(`📊 [PERFORMANCE TEST] ${stage} - ${progress}%`);
			}
		);

		console.info('✅ [PERFORMANCE TEST] 测试数据加载完成');
	} catch (error) {
		console.error('❌ [PERFORMANCE TEST] 数据加载失败:', error);
	}
};

// 清除所有数据
const clearAllData = () => {
	console.info('🗑️ [PERFORMANCE TEST] 清除所有数据');
	clearManagerData();

	// 同时清除 store 中的数据
	taskStore.tasks.splice(0);
	deliveryStore.deliveries.splice(0);

	console.info('✅ [PERFORMANCE TEST] 所有数据已清除');
};

// 生成性能报告
const generateReport = () => {
	const report = generatePerformanceReport();
	console.info('📊 [PERFORMANCE REPORT]\n' + report);

	// 在小程序中显示报告
	uni.showModal({
		title: '性能报告',
		content: '报告已生成，请查看控制台',
		showCancel: false
	});
};

// 格式化时间
const formatTime = (timestamp: number | null): string => {
	if (!timestamp) return '无';
	return new Date(timestamp).toLocaleString();
};

// 监控数据变化
watch([tasks, taskCores], () => {
	console.info(`📊 [PERFORMANCE TEST] 数据变化 - 完整任务: ${tasks.value.length}, 核心数据: ${taskCores.value.length}`);
}, { immediate: true });

onMounted(() => {
	updateMemoryUsage();
	console.info('🚀 [PERFORMANCE TEST] 性能测试页面已加载');
});
</script>

<style lang="scss" scoped>
.performance-test {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #666;
	}
}

.controls {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;

	button {
		flex: 1;
		padding: 20rpx;
		border-radius: 10rpx;
		border: none;
		background-color: #007aff;
		color: white;
		font-size: 28rpx;

		&.active {
			background-color: #28a745;
		}

		&:active {
			opacity: 0.8;
		}
	}
}

.stats {
	background-color: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;

	.section-title {
		margin-bottom: 20rpx;
		padding-bottom: 15rpx;
		border-bottom: 2rpx solid #007aff;

		text {
			font-size: 32rpx;
			font-weight: bold;
			color: #007aff;
		}
	}

	.stat-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx 0;
		border-bottom: 1rpx solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.label {
			font-size: 28rpx;
			color: #666;
		}

		.value {
			font-size: 28rpx;
			font-weight: bold;
			color: #333;

			&.success {
				color: #28a745;
			}

			&.loading {
				color: #ffc107;
			}
		}
	}
}

.task-list {
	background-color: white;
	border-radius: 10rpx;
	overflow: hidden;

	.list-header {
		padding: 30rpx;
		background-color: #f8f9fa;
		border-bottom: 1rpx solid #eee;

		text {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.scroll-container {
		height: 600rpx;
	}

	.task-item {
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.task-info {
			display: flex;
			gap: 20rpx;
			align-items: center;

			.task-id {
				font-size: 24rpx;
				color: #999;
				min-width: 120rpx;
			}

			.task-name {
				font-size: 28rpx;
				color: #333;
				flex: 1;
			}

			.task-type {
				font-size: 24rpx;
				color: #007aff;
				padding: 8rpx 16rpx;
				background-color: #e3f2fd;
				border-radius: 20rpx;
			}
		}
	}
}
</style>
