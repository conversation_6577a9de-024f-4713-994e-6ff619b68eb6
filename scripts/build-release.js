#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 发布构建脚本
 */
class ReleaseBuild {
  constructor() {
    this.rootDir = path.resolve(__dirname, '..');
    this.envFile = path.join(this.rootDir, 'utils/env.ts');
    this.manifestFile = path.join(this.rootDir, 'manifest.json');
  }

  /**
   * 执行发布构建
   */
  async build() {
    console.info('🚀 开始构建正式版...\n');

    try {
      // 1. 检查环境配置
      await this.checkEnvironment();

      // 2. 更新版本信息
      await this.updateVersion();

      // 3. 清理临时文件
      await this.cleanup();

      // 4. 验证配置
      await this.validateConfig();

      console.info('✅ 正式版构建完成！');
      console.info('\n📋 接下来的步骤：');
      console.info('1. 在HBuilderX中选择"发行" -> "原生App-云打包"');
      console.info('2. 选择正式版配置');
      console.info('3. 填写应用签名信息');
      console.info('4. 点击打包');
      console.info('5. 下载并测试APK/IPA文件\n');

    } catch (error) {
      console.error('❌ 构建失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 检查环境配置
   */
  async checkEnvironment() {
    console.info('🔍 检查环境配置...');

    const envContent = fs.readFileSync(this.envFile, 'utf8');

    // 检查是否为生产环境
    if (!envContent.includes("CURRENT_ENV: keyof typeof ENV = 'production'")) {
      throw new Error('环境配置未切换到生产环境！请确保 CURRENT_ENV = "production"');
    }

    // 检查API地址
    if (envContent.includes('192.168.') || envContent.includes('localhost')) {
      console.warn('⚠️  警告：检测到本地API地址，请确认是否正确');
    }

    console.info('✅ 环境配置检查通过');
  }

  /**
   * 更新版本信息
   */
  async updateVersion() {
    console.info('📝 检查版本信息...');

    const manifestContent = fs.readFileSync(this.manifestFile, 'utf8');
    const manifest = JSON.parse(manifestContent);

    console.info(`📱 应用名称: ${manifest.name}`);
    console.info(`🔢 版本号: ${manifest.versionName} (${manifest.versionCode})`);
    console.info(`🆔 应用ID: ${manifest.appid}`);

    console.info('✅ 版本信息确认');
  }

  /**
   * 清理临时文件
   */
  async cleanup() {
    console.info('🧹 清理临时文件...');

    const filesToClean = [
      'unpackage/dist',
      'unpackage/cache',
      '.DS_Store'
    ];

    filesToClean.forEach(file => {
      const filePath = path.join(this.rootDir, file);
      if (fs.existsSync(filePath)) {
        try {
          if (fs.statSync(filePath).isDirectory()) {
            fs.rmSync(filePath, { recursive: true, force: true });
          } else {
            fs.unlinkSync(filePath);
          }
          console.info(`🗑️  已删除: ${file}`);
        } catch (error) {
          console.warn(`⚠️  无法删除 ${file}:`, error.message);
        }
      }
    });

    console.info('✅ 临时文件清理完成');
  }

  /**
   * 验证配置
   */
  async validateConfig() {
    console.info('🔍 验证最终配置...');

    // 检查必要文件
    const requiredFiles = [
      'pages.json',
      'manifest.json',
      'App.vue',
      'main.js',
      'utils/env.ts'
    ];

    requiredFiles.forEach(file => {
      const filePath = path.join(this.rootDir, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`缺少必要文件: ${file}`);
      }
    });

    console.info('✅ 配置验证通过');
  }
}

// 执行构建
const builder = new ReleaseBuild();
builder.build().catch(console.error);
