/**
 * 性能指标接口
 */
export interface PerformanceMetric {
	name: string;
	startTime: number;
	endTime?: number;
	duration?: number;
	category: 'data-loading' | 'rendering' | 'computation' | 'user-interaction' | 'network';
	metadata?: Record<string, any>;
}

/**
 * 性能统计信息
 */
export interface PerformanceStats {
	totalMetrics: number;
	averageDuration: number;
	maxDuration: number;
	minDuration: number;
	byCategory: Record<string, {
		count: number;
		totalDuration: number;
		averageDuration: number;
		maxDuration: number;
		minDuration: number;
	}>;
	recentMetrics: PerformanceMetric[];
	memoryUsage?: {
		used: number;
		total: number;
		percentage: number;
	};
}

/**
 * 性能阈值配置
 */
export interface PerformanceThresholds {
	dataLoading: number; // 数据加载阈值（毫秒）
	rendering: number; // 渲染阈值（毫秒）
	computation: number; // 计算阈值（毫秒）
	userInteraction: number; // 用户交互阈值（毫秒）
	network: number; // 网络请求阈值（毫秒）
}

/**
 * 性能警告信息
 */
export interface PerformanceWarning {
	metric: PerformanceMetric;
	threshold: number;
	message: string;
	timestamp: number;
	severity: 'low' | 'medium' | 'high';
}

/**
 * 性能监控器
 * 提供全面的性能监控和分析功能
 */
export class PerformanceMonitor {
	private metrics: Map<string, PerformanceMetric> = new Map();
	private completedMetrics: PerformanceMetric[] = [];
	private warnings: PerformanceWarning[] = [];
	private readonly MAX_METRICS = 1000; // 最大保存的指标数量
	private readonly MAX_WARNINGS = 100; // 最大保存的警告数量

	// 默认性能阈值
	private thresholds: PerformanceThresholds = {
		dataLoading: 3000, // 3秒
		rendering: 100, // 100毫秒
		computation: 500, // 500毫秒
		userInteraction: 50, // 50毫秒
		network: 5000 // 5秒
	};

	private warningCallbacks: ((warning: PerformanceWarning) => void)[] = [];

	constructor() {
		console.info('📊 [PERFORMANCE] 性能监控器初始化完成');
	}

	/**
	 * 开始性能监控
	 */
	start(name: string, category: PerformanceMetric['category'], metadata?: Record<string, any>): void {
		const metric: PerformanceMetric = {
			name,
			startTime: performance.now(),
			category,
			metadata
		};

		this.metrics.set(name, metric);
		console.info(`📊 [PERFORMANCE] 开始监控: ${name} (${category})`);
	}

	/**
	 * 结束性能监控
	 */
	end(name: string): PerformanceMetric | null {
		const metric = this.metrics.get(name);
		if (!metric) {
			console.warn(`⚠️ [PERFORMANCE] 未找到监控项: ${name}`);
			return null;
		}

		metric.endTime = performance.now();
		metric.duration = metric.endTime - metric.startTime;

		// 移除正在进行的监控
		this.metrics.delete(name);

		// 添加到完成的监控列表
		this.completedMetrics.push(metric);

		// 限制保存的监控数量
		if (this.completedMetrics.length > this.MAX_METRICS) {
			this.completedMetrics.shift();
		}

		// 检查性能阈值
		this.checkThreshold(metric);

		console.info(`📊 [PERFORMANCE] 监控完成: ${name}, 耗时: ${metric.duration!.toFixed(2)}ms`);
		return metric;
	}

	/**
	 * 记录即时性能指标
	 */
	record(name: string, duration: number, category: PerformanceMetric['category'], metadata?: Record<string, any>): PerformanceMetric {
		const now = performance.now();
		const metric: PerformanceMetric = {
			name,
			startTime: now - duration,
			endTime: now,
			duration,
			category,
			metadata
		};

		this.completedMetrics.push(metric);

		// 限制保存的监控数量
		if (this.completedMetrics.length > this.MAX_METRICS) {
			this.completedMetrics.shift();
		}

		// 检查性能阈值
		this.checkThreshold(metric);

		console.info(`📊 [PERFORMANCE] 记录指标: ${name}, 耗时: ${duration.toFixed(2)}ms`);
		return metric;
	}

	/**
	 * 测量函数执行时间
	 */
	async measure<T>(
		name: string,
		category: PerformanceMetric['category'],
		fn: () => T | Promise<T>,
		metadata?: Record<string, any>
	): Promise<T> {
		this.start(name, category, metadata);
		try {
			const result = await fn();
			this.end(name);
			return result;
		} catch (error) {
			this.end(name);
			throw error;
		}
	}

	/**
	 * 获取性能统计信息
	 */
	getStats(): PerformanceStats {
		const stats: PerformanceStats = {
			totalMetrics: this.completedMetrics.length,
			averageDuration: 0,
			maxDuration: 0,
			minDuration: Infinity,
			byCategory: {},
			recentMetrics: this.completedMetrics.slice(-10) // 最近10个指标
		};

		if (this.completedMetrics.length === 0) {
			stats.minDuration = 0;
			return stats;
		}

		let totalDuration = 0;

		// 计算总体统计
		this.completedMetrics.forEach(metric => {
			const duration = metric.duration || 0;
			totalDuration += duration;
			stats.maxDuration = Math.max(stats.maxDuration, duration);
			stats.minDuration = Math.min(stats.minDuration, duration);

			// 按分类统计
			if (!stats.byCategory[metric.category]) {
				stats.byCategory[metric.category] = {
					count: 0,
					totalDuration: 0,
					averageDuration: 0,
					maxDuration: 0,
					minDuration: Infinity
				};
			}

			const categoryStats = stats.byCategory[metric.category];
			categoryStats.count++;
			categoryStats.totalDuration += duration;
			categoryStats.maxDuration = Math.max(categoryStats.maxDuration, duration);
			categoryStats.minDuration = Math.min(categoryStats.minDuration, duration);
		});

		stats.averageDuration = totalDuration / this.completedMetrics.length;

		// 计算分类平均值
		Object.values(stats.byCategory).forEach(categoryStats => {
			categoryStats.averageDuration = categoryStats.totalDuration / categoryStats.count;
			if (categoryStats.minDuration === Infinity) {
				categoryStats.minDuration = 0;
			}
		});

		// 获取内存使用情况
		stats.memoryUsage = this.getMemoryUsage();

		return stats;
	}

	/**
	 * 获取性能警告
	 */
	getWarnings(): PerformanceWarning[] {
		return [...this.warnings];
	}

	/**
	 * 清除性能警告
	 */
	clearWarnings(): void {
		this.warnings = [];
		console.info('📊 [PERFORMANCE] 已清除所有性能警告');
	}

	/**
	 * 设置性能阈值
	 */
	setThresholds(thresholds: Partial<PerformanceThresholds>): void {
		this.thresholds = { ...this.thresholds, ...thresholds };
		console.info('📊 [PERFORMANCE] 已更新性能阈值', this.thresholds);
	}

	/**
	 * 获取性能阈值
	 */
	getThresholds(): PerformanceThresholds {
		return { ...this.thresholds };
	}

	/**
	 * 订阅性能警告
	 */
	onWarning(callback: (warning: PerformanceWarning) => void): () => void {
		this.warningCallbacks.push(callback);
		return () => {
			const index = this.warningCallbacks.indexOf(callback);
			if (index > -1) {
				this.warningCallbacks.splice(index, 1);
			}
		};
	}

	/**
	 * 获取正在进行的监控
	 */
	getActiveMetrics(): PerformanceMetric[] {
		return Array.from(this.metrics.values());
	}

	/**
	 * 清除所有性能数据
	 */
	clear(): void {
		this.metrics.clear();
		this.completedMetrics = [];
		this.warnings = [];
		console.info('📊 [PERFORMANCE] 已清除所有性能数据');
	}

	/**
	 * 导出性能数据
	 */
	export(): {
		metrics: PerformanceMetric[];
		warnings: PerformanceWarning[];
		stats: PerformanceStats;
		thresholds: PerformanceThresholds;
		exportTime: number;
	} {
		return {
			metrics: [...this.completedMetrics],
			warnings: [...this.warnings],
			stats: this.getStats(),
			thresholds: { ...this.thresholds },
			exportTime: Date.now()
		};
	}

	/**
	 * 检查性能阈值
	 */
	private checkThreshold(metric: PerformanceMetric): void {
		const threshold = this.thresholds[metric.category];
		const duration = metric.duration || 0;

		if (duration > threshold) {
			const severity = this.calculateSeverity(duration, threshold);
			const warning: PerformanceWarning = {
				metric,
				threshold,
				message: `${metric.name} 执行时间 ${duration.toFixed(2)}ms 超过阈值 ${threshold}ms`,
				timestamp: Date.now(),
				severity
			};

			this.warnings.push(warning);

			// 限制警告数量
			if (this.warnings.length > this.MAX_WARNINGS) {
				this.warnings.shift();
			}

			// 通知订阅者
			this.warningCallbacks.forEach(callback => {
				try {
					callback(warning);
				} catch (error) {
					console.error('❌ [PERFORMANCE] 警告回调执行失败:', error);
				}
			});

			console.warn(`⚠️ [PERFORMANCE] 性能警告: ${warning.message}`);
		}
	}

	/**
	 * 计算警告严重程度
	 */
	private calculateSeverity(duration: number, threshold: number): PerformanceWarning['severity'] {
		const ratio = duration / threshold;
		if (ratio >= 3) return 'high';
		if (ratio >= 2) return 'medium';
		return 'low';
	}

	/**
	 * 获取内存使用情况
	 */
	private getMemoryUsage(): { used: number; total: number; percentage: number } | undefined {
		if (typeof performance !== 'undefined' && performance.memory) {
			const used = performance.memory.usedJSHeapSize;
			const total = performance.memory.totalJSHeapSize;
			return {
				used,
				total,
				percentage: (used / total) * 100
			};
		}
		return undefined;
	}

	/**
	 * 创建性能报告
	 */
	generateReport(): string {
		const stats = this.getStats();
		const warnings = this.getWarnings();

		let report = '📊 性能监控报告\n';
		report += '='.repeat(50) + '\n\n';

		// 总体统计
		report += '📈 总体统计:\n';
		report += `  总监控次数: ${stats.totalMetrics}\n`;
		report += `  平均耗时: ${stats.averageDuration.toFixed(2)}ms\n`;
		report += `  最大耗时: ${stats.maxDuration.toFixed(2)}ms\n`;
		report += `  最小耗时: ${stats.minDuration.toFixed(2)}ms\n\n`;

		// 分类统计
		report += '📊 分类统计:\n';
		Object.entries(stats.byCategory).forEach(([category, categoryStats]) => {
			report += `  ${category}:\n`;
			report += `    次数: ${categoryStats.count}\n`;
			report += `    平均耗时: ${categoryStats.averageDuration.toFixed(2)}ms\n`;
			report += `    最大耗时: ${categoryStats.maxDuration.toFixed(2)}ms\n\n`;
		});

		// 内存使用
		if (stats.memoryUsage) {
			report += '💾 内存使用:\n';
			report += `  已使用: ${(stats.memoryUsage.used / 1024 / 1024).toFixed(2)}MB\n`;
			report += `  总计: ${(stats.memoryUsage.total / 1024 / 1024).toFixed(2)}MB\n`;
			report += `  使用率: ${stats.memoryUsage.percentage.toFixed(1)}%\n\n`;
		}

		// 性能警告
		if (warnings.length > 0) {
			report += '⚠️ 性能警告:\n';
			warnings.slice(-5).forEach(warning => {
				report += `  [${warning.severity.toUpperCase()}] ${warning.message}\n`;
			});
		}

		return report;
	}
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();
