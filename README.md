# 📱 群忠派单应用

> 基于 uni-app + Vue3 + TypeScript 开发的移动端任务管理应用

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/your-repo)
[![Platform](https://img.shields.io/badge/platform-Android%20%7C%20iOS-lightgrey.svg)](https://github.com/your-repo)
[![Framework](https://img.shields.io/badge/framework-uni--app-green.svg)](https://uniapp.dcloud.io/)

## 📋 项目简介

群忠派单应用是一款专业的移动端任务管理工具，主要用于派单管理、任务执行、图片采集和数据上传等功能。应用支持点位任务和实景任务两种类型，提供直观的任务管理界面和高效的工作流程。

### ✨ 核心功能

- 🏢 **派单管理**: 支持多派单选择和管理
- 📍 **任务执行**: 点位任务和实景任务的执行
- 📸 **图片采集**: 支持拍照和相册选择
- ☁️ **数据同步**: 自动上传和数据同步
- 📊 **统计分析**: 实时任务统计和进度跟踪
- 🎨 **个性化**: 字体大小调节和界面定制

## 🚀 最新版本 v2.0.0

### 🎉 重大更新

#### ✨ 新功能
- **任务类型视觉区分**: 点位任务(📍蓝色)和实景任务(🏢绿色)一眼可辨
- **统计数字突出显示**: 应拍、已传、待传等关键数据更加醒目
- **图片预览优化**: 点击图片直接预览，点击文字进入详情
- **两栏统计布局**: delivery页面采用左右分栏显示，信息更清晰

#### 🔧 界面优化
- **导航栏任务标识**: 在task-detail页面显示任务类型徽章
- **统计项卡片化**: 使用渐变背景和阴影效果
- **颜色语义统一**: 绿色=完成，红色=待处理，蓝色=总数
- **响应式字体**: 支持系统字体大小调节

#### 🐛 问题修复
- 修复图片点击进入详情页的问题
- 修复统计数字显示不够突出的问题
- 优化网络错误处理和提示信息
- 改进任务类型判断逻辑

### 📱 界面截图

```
┌─────────────────────────────────────┐
│  📍点位  任务列表页面                │
├─────────────────────────────────────┤
│  [📍点位] 点位编号：001 (朝阳区)     │
│  应拍:5  已传:3  待传:2             │
│                                     │
│  [🏢实景] 实景拍摄：某小区 (海淀区)   │
│  应拍:8  已传:5  待传:3             │
└─────────────────────────────────────┘
```

## 🛠️ 技术栈

- **框架**: uni-app 3.x
- **前端**: Vue 3 + TypeScript
- **状态管理**: Pinia
- **构建工具**: Vite
- **UI组件**: 自定义组件库
- **网络请求**: 封装的HTTP客户端
- **图片处理**: uni-app原生API

## 📦 项目结构

```
omaps-uniapp/
├── pages/                  # 页面文件
│   ├── delivery/           # 派单管理
│   ├── task/              # 任务管理
│   ├── images/            # 图片管理
│   ├── login/             # 登录页面
│   └── my/                # 个人中心
├── stores/                # 状态管理
│   ├── auth.ts            # 认证状态
│   ├── delivery.ts        # 派单状态
│   ├── task.ts            # 任务状态
│   ├── images.ts          # 图片状态
│   └── settings.ts        # 设置状态
├── utils/                 # 工具函数
│   ├── request.ts         # 网络请求
│   ├── api.ts             # API接口
│   ├── env.ts             # 环境配置
│   ├── logger.ts          # 日志管理
│   └── security.ts        # 安全配置
├── components/            # 公共组件
├── static/               # 静态资源
└── scripts/              # 构建脚本
```

## 🔧 开发环境

### 环境要求

- **Node.js**: >= 14.0.0
- **HBuilderX**: 最新版本
- **uni-app**: 3.x
- **Vue**: 3.x

### 快速开始

1. **克隆项目**
```bash
git clone [项目地址]
cd omaps-uniapp
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境**
```typescript
// utils/env.ts
const CURRENT_ENV = 'development'; // 开发环境
```

4. **启动开发**
- 在HBuilderX中打开项目
- 选择运行到手机或模拟器
- 开始开发调试

## 🚀 发布部署

### 发布前准备

1. **环境切换**
```typescript
// utils/env.ts
const CURRENT_ENV = 'production'; // 切换到生产环境
```

2. **运行发布脚本**
```bash
npm run build:release
```

3. **检查清单**
- [ ] 环境配置正确
- [ ] 版本号已更新
- [ ] 功能测试通过
- [ ] 性能测试正常

### HBuilderX打包

1. **选择发行方式**
   - 发行 → 原生App-云打包

2. **配置参数**
   ```
   应用名称: 群忠派单
   版本名称: 2.0.0
   版本号: 200
   ```

3. **证书配置**
   - Android: 配置正式版签名证书
   - iOS: 配置开发者证书和描述文件

4. **开始打包**
   - 点击打包按钮
   - 等待云端打包完成
   - 下载安装包

### 发布验证

- [ ] 真机安装测试
- [ ] 核心功能验证
- [ ] 网络连接测试
- [ ] 性能表现检查

## 📊 版本历史

### v2.0.0 (2024-01-XX)
- 🎉 重大界面优化和功能增强
- ✨ 新增任务类型视觉区分
- 🔧 统计数字突出显示
- 🐛 修复图片交互问题

### v1.0.0 (2023-XX-XX)
- 🎉 首个正式版本发布
- ✨ 基础功能实现
- 📱 支持Android和iOS平台

## 🔧 配置说明

### 环境配置

```typescript
// utils/env.ts
const ENV = {
  development: {
    API_URL: 'http://**************:8848',  // 开发环境
    OSS_URL: 'https://minio.omaps.cn'
  },
  production: {
    API_URL: 'https://api.omaps.cn',        // 生产环境
    OSS_URL: 'https://minio.omaps.cn'
  }
};
```

### 应用配置

```json
// manifest.json
{
  "name": "群忠派单",
  "versionName": "2.0.0",
  "versionCode": "200",
  "appid": "__UNI__0119C96"
}
```

## 📖 使用指南

### 基本操作流程

1. **登录应用**
   - 输入用户名和密码
   - 系统自动保存登录状态

2. **选择派单**
   - 在派单页面选择要执行的派单
   - 支持多派单同时选择

3. **执行任务**
   - 查看任务列表
   - 点击任务进入详情
   - 拍照或选择图片
   - 上传完成任务

4. **查看进度**
   - 实时查看统计数据
   - 筛选不同状态的任务
   - 导出任务报告

### 功能特色

#### 🎯 任务类型识别
- **点位任务**: 📍蓝色标识，用于具体位置拍摄
- **实景任务**: 🏢绿色标识，用于建筑物整体拍摄

#### 📊 统计数据
- **应拍**: 需要拍摄的总数量
- **已传**: 已上传到服务器的数量
- **待传**: 已拍摄但未上传的数量

#### 🔧 个性化设置
- **字体大小**: 支持标准、大号、超大号
- **上传模式**: WiFi模式或移动网络模式
- **任务模式**: 普通模式或快拍模式

## 🔍 故障排除

### 常见问题

#### 1. 登录失败
**问题**: 无法登录或提示网络错误
**解决方案**:
- 检查网络连接
- 确认用户名密码正确
- 联系管理员检查服务器状态

#### 2. 图片上传失败
**问题**: 图片无法上传或上传超时
**解决方案**:
- 检查网络连接质量
- 切换到WiFi网络
- 重新尝试上传

#### 3. 任务数据不同步
**问题**: 任务列表不更新或数据错误
**解决方案**:
- 下拉刷新任务列表
- 重新登录应用
- 清除应用缓存

#### 4. 应用崩溃或卡顿
**问题**: 应用运行不稳定
**解决方案**:
- 重启应用
- 清理手机内存
- 更新到最新版本

### 性能优化建议

- 📱 定期清理应用缓存
- 🔋 在WiFi环境下进行大量上传
- 💾 及时清理本地图片
- 🔄 保持应用版本最新

## 🔐 安全说明

### 数据安全
- 所有网络传输使用HTTPS加密
- 用户凭证安全存储
- 图片数据加密上传
- 定期清理敏感信息

### 隐私保护
- 仅收集必要的业务数据
- 不获取用户个人隐私信息
- 位置信息仅用于任务定位
- 图片仅用于业务需求

## 🚀 更新日志

### 即将发布 (v2.1.0)
- [ ] 离线模式支持
- [ ] 批量操作功能
- [ ] 数据导出功能
- [ ] 更多筛选选项

### 开发中功能
- [ ] 语音备注功能
- [ ] 团队协作功能
- [ ] 智能推荐功能
- [ ] 数据分析报表

## 🐛 问题反馈

如果您在使用过程中遇到问题，请通过以下方式反馈：

- 📧 邮箱: [<EMAIL>]
- 📱 电话: [联系电话]
- 🐛 Issues: [GitHub Issues地址]
- 💬 微信群: [群二维码]

### 反馈模板

```
问题描述: [详细描述遇到的问题]
复现步骤: [如何重现这个问题]
设备信息: [手机型号、系统版本]
应用版本: [当前使用的应用版本]
截图附件: [相关截图]
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 开发团队

- **项目负责人**: [姓名] - [邮箱]
- **前端开发**: [姓名] - [邮箱]
- **后端开发**: [姓名] - [邮箱]
- **UI设计**: [姓名] - [邮箱]
- **测试工程师**: [姓名] - [邮箱]

## 🙏 致谢

感谢以下开源项目和技术支持：

- [uni-app](https://uniapp.dcloud.io/) - 跨平台开发框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Pinia](https://pinia.vuejs.org/) - Vue状态管理库
- [TypeScript](https://www.typescriptlang.org/) - JavaScript超集
- [Vite](https://vitejs.dev/) - 下一代前端构建工具

---

**📱 扫码下载最新版本**

[这里可以放置二维码图片]

wx21aea6564045acfc
c9c8bca22a583c9cd418f2750989be6c

**🌟 如果这个项目对您有帮助，请给我们一个Star！**

---

© 2024 群忠派单应用. All rights reserved.
