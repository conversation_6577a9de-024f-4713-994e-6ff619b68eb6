import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any

// 用户设置类型定义
export interface UserSettings {
  fontSize: 'normal' | 'large' | 'xlarge';
  orderPeriod: 'today' | 'week' | 'halfMonth' | 'month';
  taskMode: 'normal' | 'quick';
  wifiOnly: boolean;
  completedTasksBottom: boolean;
  // 图片显示模式设置
  imageDisplayMode: 'smart' | 'dataSaver' | 'standard';
  // 任务显示设置
  taskDisplaySettings: {
    taskType: string;
    photoStatus: string;
    completionStatus: string;
    mergeZoneTasks: boolean;
    taskSortMode: string;
  };
  // 派单显示设置
  deliveryDisplaySettings?: {
    completionStatus: string;
  };
}

// 默认设置
export const defaultSettings: UserSettings = {
  fontSize: 'normal',    // 默认：标准
  orderPeriod: 'week',   // 默认：前后一周
  taskMode: 'normal',    // 默认：常规模式
  wifiOnly: true,        // 默认：仅WIFI上传
  completedTasksBottom: false,  // 默认：已完成待上传任务不置底
  imageDisplayMode: 'smart',    // 默认：智能模式（WiFi显示图片，移动网络省流）
  // 任务显示设置默认值
  taskDisplaySettings: {
    taskType: 'all',              // 默认：全部任务
    photoStatus: 'all',           // 默认：全部
    completionStatus: 'notComplete',      // 默认：未完成
    mergeZoneTasks: true,         // 默认：合并实景任务
    taskSortMode: 'mixed'         // 默认：按地址从大到小混排
  },
  // 派单显示设置默认值
  deliveryDisplaySettings: {
    completionStatus: 'incomplete'  // 默认：仅显示未完成
  }
};

// 本地存储的key
const SETTINGS_STORAGE_KEY = 'userSettings';

export const useSettingsStore = defineStore('settings', () => {
  // 用户设置状态
  const settings = reactive<UserSettings>({ ...defaultSettings });

  // 从本地存储加载设置
  const loadSettings = () => {
    try {
      const savedSettings = uni.getStorageSync(SETTINGS_STORAGE_KEY);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        Object.assign(settings, parsedSettings);
      }
    } catch (e) {
      console.error('加载用户设置失败', e);
    }
  };

  // 保存设置到本地存储
  const saveSettings = () => {
    try {
      uni.setStorageSync(SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    } catch (e) {
      console.error('保存用户设置失败', e);
    }
  };

  // 更新单个设置项
  const updateSetting = <K extends keyof UserSettings>(key: K, value: UserSettings[K]) => {
    settings[key] = value;
    saveSettings();

    // 应用字体大小设置
    if (key === 'fontSize') {
      applyFontSize(value as 'normal' | 'large' | 'xlarge');
    }
  };

  // 应用字体大小设置
  const applyFontSize = (size: 'normal' | 'large' | 'xlarge') => {
    // 保存到本地存储
    uni.setStorageSync('appFontSize', size);

    // 设置字体缩放比例 - 针对30-45岁人群优化
    const scaleMap = {
      'normal': 1.0,    // 标准字体，保持信息密度和专业感
      'large': 1.15,    // 大字体适度提升，兼顾可读性和效率
      'xlarge': 1.25     // 特大字体，满足个别用户需求
    };

    const scale = scaleMap[size];

    // H5环境中设置CSS类
    // #ifdef H5
    if (typeof document !== 'undefined' && document.documentElement) {
      // 移除所有字体大小类
      document.documentElement.classList.remove('font-size-normal', 'font-size-large', 'font-size-xlarge');
      // 添加新的字体大小类
      document.documentElement.classList.add(`font-size-${size}`);
      // 同时设置CSS变量作为备用
      document.documentElement.style.setProperty('--font-scale', scale.toString());
    }
    // #endif

    // 移动端环境中通过CSS变量设置字体大小
    // #ifndef H5
    try {
      // 通过uni-app的方式设置页面样式
      // 我们需要通过全局事件来通知所有页面更新字体大小
      uni.$emit('fontSizeChanged', { size, scale });

      // 保存缩放比例到全局存储，供页面使用
      uni.setStorageSync('fontScale', scale);

      console.info(`📱 [FONT] 移动端字体大小已设置: ${size} (缩放: ${scale})`);
    } catch (error) {
      console.error('移动端字体大小设置失败:', error);
    }
    // #endif

    console.info(`🎨 [FONT] 字体大小已应用: ${size} (缩放比例: ${scale})`);
  };

  // 重置为默认设置
  const resetSettings = () => {
    Object.assign(settings, defaultSettings);
    saveSettings();
    applyFontSize(defaultSettings.fontSize);
  };

  // 初始化时加载设置
  loadSettings();

  return {
    settings,
    updateSetting,
    loadSettings,
    saveSettings,
    resetSettings,
    applyFontSize
  };
});
