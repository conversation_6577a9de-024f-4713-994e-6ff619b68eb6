<template>
	<view class="task-detail-container" :style="dynamicFontStyle">
		<!-- 页面元数据，用于设置导航栏样式 -->
		<page-meta>
			<navigation-bar :title="taskTitle" front-color="#ffffff" background-color="#2196F3" />
		</page-meta>

		<!-- 自定义导航栏 -->
		<view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-left" @click="goBack">
				<text class="back-icon iconfont icon-back"></text>
			</view>
			<view class="navbar-center">
				<!-- 任务类型标识 -->
				<view class="task-type-badge" :class="{ 'zone-task': isZoneTask, 'spot-task': !isZoneTask }">
					<text class="task-type-icon iconfont" :class="isZoneTask ? 'icon-house' : 'icon-point'"></text>
					<text class="task-type-text">{{ isZoneTask ? '实景任务' : '点位任务' }}</text>
				</view>
			</view>
			<view class="navbar-right">
				<!-- 移除了"进入拍照"按钮，因为现在有底部固定按钮 -->
			</view>
		</view>

		<!-- 任务详情内容 -->
		<scroll-view
			scroll-y
			class="task-detail-content"
			:style="{ marginTop: statusBarHeight + 44 + 'px', paddingBottom: '60px' }">
			<!-- 派单信息 -->
			<view class="detail-section">
				<view class="section-label">{{ deliveryTitle }}</view>
				<view class="section-value delivery-list">
					<view v-for="(deliveryName, index) in deliveryNames" :key="index" class="delivery-item">
						{{ deliveryName }}
					</view>
				</view>
			</view>

			<!-- 点位信息（仅点位任务显示） -->
			<view v-if="!isZoneTask" class="detail-section">
				<view class="section-label">点位</view>
				<view class="section-value">
					{{ task?.spotCode || '未知' }} ({{ task?.belongDistrict || '未知' }})
				</view>
			</view>

			<!-- 小区信息 -->
			<view class="detail-section">
				<view class="section-label">小区</view>
				<view class="section-value">
					{{ (task?.zoneName || '未知小区') + '(' + (task?.belongDistrict || '未知区域') + ')' }}
				</view>
			</view>

			<!-- 地址信息 -->
			<view class="detail-section address-section">
				<view class="section-value address-value">
					{{ task ? getShotLocation(task) : '未知' }}
				</view>
			</view>

			<!-- 拍照要求 -->
			<view class="detail-section">
				<view class="section-label">拍照要求</view>
				<view class="section-value">
					{{ task?.zonePhotoRequirement || '小区大门不带报纸' }}
				</view>
			</view>

			<!-- 点位任务：上次画面和本次画面并排 -->
			<view v-if="!isZoneTask" class="detail-section image-section side-by-side">
				<view class="image-column">
					<view class="section-label image-label previous-label">上次画面</view>
					<view class="image-container">
						<image
							v-if="task?.previousContentImageUrl"
							:src="task.previousContentImageUrl"
							mode="aspectFit"
							@load="handleImageLoad('previous')"
							@error="handleImageError('previous')"
							@click="previewImage(task?.previousContentImageUrl)"
							class="content-image">
						</image>
						<view v-else class="empty-image">
							<text class="empty-image-text">未上传画面</text>
						</view>
					</view>
				</view>

				<view class="image-column">
					<view class="section-label image-label current-label">本次画面</view>
					<view class="image-container">
						<image
							v-if="task?.currentContentImageUrl"
							:src="task.currentContentImageUrl"
							mode="aspectFit"
							@load="handleImageLoad('current')"
							@error="handleImageError('current')"
							@click="previewImage(task?.currentContentImageUrl)"
							class="content-image">
						</image>
						<view v-else class="empty-image">
							<text class="empty-image-text">未上传画面</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 实景任务：历史实景显示（仅在没有已上传照片时显示） -->
			<view v-if="isZoneTask && currentTaskServerTakenImages.length === 0" class="detail-section">
				<view class="section-label">历史实景 （{{ historicalImages.length }}）</view>
				<view class="section-value">
					<view v-if="historicalImages.length > 0" class="historical-images-grid">
						<view
							v-for="(imageUrl, index) in historicalImages"
							:key="index"
							class="historical-image-item"
							@click="showHistoricalImageActions(imageUrl)">
							<view class="historical-image-container">
								<image
									:src="imageUrl"
									mode="aspectFill"
									class="historical-image"
									@error="handleHistoricalImageError(index)">
								</image>
								<view class="historical-image-overlay">
									<text class="historical-image-index">{{ index + 1 }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-historical">
						<text class="empty-historical-text">暂无历史实景</text>
					</view>
				</view>
			</view>

			<!-- 已上传照片 -->
			<view class="detail-section">
				<view class="section-label">已上传照片 （{{ currentTaskServerTakenImages.length }}）</view>
				<view class="section-value photo-list">
					<view v-if="currentTaskServerTakenImages.length > 0" class="pending-photo-list">
						<view
							v-for="(image, index) in currentTaskServerTakenImages"
							:key="image.id || index"
							class="pending-photo-item"
							@click="showServerImageActions(image)">
							<view class="photo-thumbnail">
								<image :src="OssApi.getOssImageUrl(image.imageUrl)" mode="aspectFill"></image>
							</view>
							<view class="photo-info">
								<text class="photo-time-label">拍照时间：</text>
								<text class="photo-time">{{ formatImageTime(image.shotTime) }}</text>
							</view>
							<view class="arrow-icon">
								<text>></text>
							</view>
						</view>
					</view>
					<view v-else class="empty-photos">
						<text>暂无已上传照片</text>
					</view>
				</view>
			</view>

			<!-- 待上传照片 -->
			<view class="detail-section">
				<view class="section-label">待上传队列照片 （{{ currentTaskLocalPendingImages.length }}）</view>
				<view class="section-value photo-list">
					<view v-if="currentTaskLocalPendingImages.length > 0" class="pending-photo-list">
						<view
							v-for="(image, index) in currentTaskLocalPendingImages"
							:key="image.id || index"
							class="pending-photo-item"
							@click="showImageActions(image)">
							<view class="photo-thumbnail">
								<image :src="OssApi.getOssImageUrl(image.imageUrl)" mode="aspectFill"></image>
							</view>
							<view class="photo-info">
								<text class="photo-time-label">拍照时间：</text>
								<text class="photo-time">{{ formatImageTime(image.shotTime) }}</text>
							</view>
							<view class="arrow-icon">
								<text>></text>
							</view>
						</view>
					</view>
					<view v-else class="empty-photos">
						<text>暂无待上传照片</text>
					</view>
				</view>
			</view>

			<!-- 任务统计信息 -->
			<view class="detail-section">
				<view class="section-label stats-section-label">拍照统计</view>
				<view class="section-value stats-info">
					<view class="stats-item">
						<text class="stats-label highlight-label">应拍：</text>
						<text class="stats-value highlight-value">{{ currentTaskStats.shouldTake }}</text>
					</view>
					<view class="stats-item">
						<text class="stats-label highlight-label">已传：</text>
						<text class="stats-value highlight-value uploaded">{{ currentTaskStats.hasTaken }}</text>
					</view>
					<view class="stats-item">
						<text class="stats-label highlight-label">待传：</text>
						<text class="stats-value highlight-value pending">{{ currentTaskStats.pendingUpload }}</text>
					</view>
				</view>
				<view class="section-value completion-status">
					<text class="completion-text highlight-status" :class="{ 'completed': taskStatus.completed }">
						<text class="iconfont" :class="taskStatus.icon"></text>
						{{ ' ' + taskStatus.text }}
					</text>
				</view>
			</view>
		</scroll-view>

		<!-- 底部固定按钮区域 -->
		<view class="footer-buttons">
			<view
				class="footer-button camera-button"
				:class="{ 'single-button': task?.onlyPhoto !== false }"
				@click="handleTakePhoto">
				<view class="button-icon">
					<text class="iconfont icon-camera"></text>
				</view>
				<text class="button-text">拍照</text>
			</view>
			<view v-if="task?.onlyPhoto === false" class="footer-button album-button" @click="handleChooseFromAlbum">
				<view class="button-icon">
					<text class="iconfont icon-album"></text>
				</view>
				<text class="button-text">从图库选择</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, computed, nextTick } from 'vue';
import { useTaskStore, useImagesStore, useDeliveryStore } from '../../stores';
import { showToast } from '../../utils';
import { TaskUtils } from '../../utils/task';
import { OssApi } from '../../utils/api';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;
declare const getCurrentPages: () => any[];

// 获取页面参数
const taskId = ref('');
const task = ref<any>(null);

// 状态栏高度
const statusBarHeight = ref(20);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
  '--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
  try {
    const savedFontScale = uni.getStorageSync('fontScale') || 1;
    currentFontScale.value = savedFontScale;
    console.info(`📄 [TASK-DETAIL] 页面字体大小初始化: 缩放 ${savedFontScale}`);
  } catch (error) {
    console.error('页面字体大小初始化失败:', error);
  }
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
  const { size, scale } = data;
  currentFontScale.value = scale;
  console.info(`📄 [TASK-DETAIL] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 计算属性
const isZoneTask = computed(() => task.value?.spotId === null);
const taskTitle = computed(() => (isZoneTask.value ? '拍摄实景' : '点位任务'));

// 派单信息
const deliveryNames = ref<string[]>([]);
const deliveryTitle = ref('派单任务');

// 图片状态
const imageState = reactive({
	loading: { previous: false, current: false },
	error: { previous: false, current: false },
});

// 获取任务和图片数据
const taskStore = useTaskStore();
const imagesStore = useImagesStore();
const { getShotLocation } = taskStore;
const {
	taskLocalPendingmages,
	getTaskStats,
	addLocalImage,
	deleteLocalImage,
} = imagesStore;

// 获取派单store
const deliveryStore = useDeliveryStore();
const { getDeliveryName } = deliveryStore;

// 缓存相关
const taskDataCache = ref({
	localImages: [] as any[],
	serverImages: [] as any[],
	stats: {
		shouldTake: 0,
		hasTaken: 0,
		hasUploaded: 0,
		pendingUpload: 0,
	},
	lastUpdateTime: 0,
});

const CACHE_DURATION = 1000; // 1秒缓存

// 优化的计算属性：当前任务的图片和统计信息（带缓存）
const currentTaskLocalPendingImages = computed(() => {
	if (!taskId.value) return [];

	const now = Date.now();
	if (now - taskDataCache.value.lastUpdateTime < CACHE_DURATION) {
		return taskDataCache.value.localImages;
	}

	const images = taskLocalPendingmages(taskId.value);
	taskDataCache.value.localImages = images;
	taskDataCache.value.lastUpdateTime = now;
	return images;
});

const currentTaskServerTakenImages = computed(() => {
	if (!taskId.value) return [];

	const now = Date.now();
	if (now - taskDataCache.value.lastUpdateTime < CACHE_DURATION) {
		return taskDataCache.value.serverImages;
	}

	const images = task.value?.taskImages || [];
	taskDataCache.value.serverImages = images;
	return images;
});

const currentTaskStats = computed(() => {
	if (!taskId.value || !task.value) {
		return {
			shouldTake: 0,
			hasTaken: 0,
			hasUploaded: 0,
			pendingUpload: 0,
		};
	}

	const now = Date.now();
	if (now - taskDataCache.value.lastUpdateTime < CACHE_DURATION) {
		return taskDataCache.value.stats;
	}

	const stats = getTaskStats(taskId.value, task.value.photoMax || 0);
	taskDataCache.value.stats = stats;
	return stats;
});

// 计算属性：历史实景图片列表（仅实景任务）
const historicalImages = computed(() => {
	if (!isZoneTask.value || !task.value?.previousContentImageUrl) {
		return [];
	}

	// 解析 previousContentImageUrl，支持分号和竖线分隔符
	const imageUrls = task.value.previousContentImageUrl
		.split(/[;|]/)
		.map((url: string) => url.trim())
		.filter((url: string) => url.length > 0);

	return imageUrls;
});

// 计算任务状态
const taskStatus = computed(() => {
	const stats = currentTaskStats.value;
	const shouldTake = stats.shouldTake; // 应拍
	const hasTaken = stats.hasTaken; // 已拍（已上传到服务器）
	const pendingUpload = stats.pendingUpload; // 待传

	// 如果 应拍 = 已拍，显示 已完成
	if (shouldTake === hasTaken) {
		return { text: '已完成', icon: 'icon-check', completed: true };
	}

	// 如果 应拍 > 已拍，待传 = 0，显示 等待拍照
	if (shouldTake > hasTaken && pendingUpload === 0) {
		return { text: '等待拍照', icon: 'icon-camera', completed: false };
	}

	// 其他情况：待传 > 0
	if (pendingUpload > 0) {
		// 检查是否已启动上传
		const uploadStatus = imagesStore.getUploadStatus();
		if (uploadStatus.isUploading) {
			return { text: '上传中', icon: 'icon-upload', completed: false };
		} else {
			return { text: '等待上传', icon: 'icon-clock', completed: false };
		}
	}

	// 默认状态
	return { text: '进行中', icon: 'icon-clock', completed: false };
});

// 保持向后兼容性
const isTaskLocallyComplete = computed(() => taskStatus.value.completed);

// 派单信息缓存
const deliveryInfoCache = ref({
	names: [] as string[],
	title: '',
	taskId: '',
});

// 优化的派单信息处理（带缓存）
const processDeliveryInfo = () => {
	if (!task.value) return;

	// 如果任务ID相同，使用缓存
	if (deliveryInfoCache.value.taskId === task.value.taskId) {
		deliveryNames.value = deliveryInfoCache.value.names;
		deliveryTitle.value = deliveryInfoCache.value.title;
		return;
	}

	const allTasks = [task.value, ...(task.value?.collapsedZoneTasks || [])];
	const uniqueDeliveryIds = [...new Set(allTasks.map((t: any) => t.deliveryId))];

	// 使用 Map 优化查找性能
	const deliveryMap = new Map();
	deliveryStore.deliveries.forEach((d: any) => {
		deliveryMap.set(d.deliveryId, d);
	});

	const names = uniqueDeliveryIds.map((deliveryId) => {
		const delivery = deliveryMap.get(deliveryId);
		return delivery ? getDeliveryName(delivery) : `派单: ${deliveryId}`;
	});

	const title = '派单任务' + (uniqueDeliveryIds.length > 1 ? `（${uniqueDeliveryIds.length}个）` : '');

	// 更新缓存
	deliveryInfoCache.value = {
		names,
		title,
		taskId: task.value.taskId,
	};

	deliveryNames.value = names;
	deliveryTitle.value = title;
};

// 处理图片加载事件
const handleImageLoad = (type: 'previous' | 'current') => {
	imageState.loading[type] = false;
};

// 处理图片加载错误事件
const handleImageError = (type: 'previous' | 'current') => {
	imageState.loading[type] = false;
	imageState.error[type] = true;
};

// 预览图片
const previewImage = (url: string) => {
	if (!url) return;
	uni.previewImage({
		urls: [url],
		current: url,
	});
};

// 格式化图片时间
const formatImageTime = (shotTime: Date | string) => {
	if (!shotTime) return '未知时间';

	const date = new Date(shotTime);
	if (isNaN(date.getTime())) return '未知时间';

	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');

	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 显示本地图片操作菜单
const showImageActions = (image: any) => {
	uni.showActionSheet({
		itemList: ['查看', '存到手机', '删除', '取消'],
		itemColor: '#007AFF',
		success: (res: any) => {
			switch (res.tapIndex) {
				case 0: // 查看
					handleViewImage(image);
					break;
				case 1: // 存到手机
					handleSaveToAlbum(image);
					break;
				case 2: // 删除
					handleDeleteImage(image);
					break;
				case 3: // 取消
					// 用户取消，不执行任何操作
					break;
			}
		},
	});
};

// 显示服务端图片操作菜单
const showServerImageActions = (image: any) => {
	uni.showActionSheet({
		itemList: ['查看', '存到手机', '取消'],
		itemColor: '#007AFF',
		success: (res: any) => {
			switch (res.tapIndex) {
				case 0: // 查看
					handleViewImage(image);
					break;
				case 1: // 存到手机
					handleSaveToAlbum(image);
					break;
				case 2: // 取消
					// 用户取消，不执行任何操作
					break;
			}
		},
	});
};

// 显示历史实景图片操作菜单
const showHistoricalImageActions = (imageUrl: string) => {
	uni.showActionSheet({
		itemList: ['查看', '设为任务同景', '取消'],
		itemColor: '#007AFF',
		success: (res: any) => {
			switch (res.tapIndex) {
				case 0: // 查看
					previewImage(imageUrl);
					break;
				case 1: // 设为任务同景
					handleSetAsTaskImage(imageUrl);
					break;
				case 2: // 取消
					// 用户取消，不执行任何操作
					break;
			}
		},
	});
};

// 处理历史实景图片加载错误
const handleHistoricalImageError = (index: number) => {
	console.error(`历史实景图片 ${index + 1} 加载失败`);
};

// 设置历史图片为任务同景
const handleSetAsTaskImage = async (imageUrl: string) => {
	try {
		// 检查是否可以添加照片
		if (!canAddPhoto()) return;
		await processHistoricalImage(imageUrl);
	} catch (error) {
		console.error('设置任务同景失败:', error);
		showToast('设置任务同景失败', 'error');
	}
};

// 处理历史图片（与拍照后处理一致，但跳过上传）
const processHistoricalImage = async (imageUrl: string) => {
	try {

		// 创建完整的任务图片对象，使用历史图片URL
		const newTaskImage = TaskUtils.createTaskImage({
			zoneId: task.value.zoneId,
			taskId: task.value.taskId,
			spotId: task.value.spotId,
			spotCode: task.value.spotCode,
			imageUrl, // 使用历史图片地址
			task: task.value,
		});

		// 设置跳过上传标志，因为是历史图片
		newTaskImage.skipUpload = true;

		// 添加到本地图片列表
		addLocalImage(task, newTaskImage);

		// 显示成功提示
		showToast('已设为任务同景', 'success');

		// 清除缓存，强制重新计算
		clearCache();

		console.info('历史图片已设为任务同景:', {
			taskId: task.value.taskId,
			imageUrl,
			skipUpload: true
		});
	} catch (error) {
		console.error('处理历史图片失败:', error);
		showToast('处理历史图片失败', 'error');
	}
};

// 查看图片
const handleViewImage = (image: any) => {
	previewImage(image.imageUrl);
};

// 保存图片到相册
const handleSaveToAlbum = (image: any) => {
	uni.showLoading({ title: '保存中...' });

	const saveConfig = {
		filePath: image.imageUrl,
		success: () => showToast('保存成功', 'success'),
		fail: (err: any) => {
			console.error('保存失败:', err);
			showToast('保存失败', 'error');
		},
		complete: () => uni.hideLoading(),
	};

	// #ifdef APP-PLUS || MP-WEIXIN || MP-QQ || MP-TOUTIAO
	uni.saveImageToPhotosAlbum(saveConfig);
	// #endif

	// #ifdef H5
	// H5环境下使用canvas转换后下载
	const downloadImageForH5 = () => {
		const img = new Image();
		img.crossOrigin = 'anonymous';

		img.onload = () => {
			const canvas = document.createElement('canvas');
			canvas.width = img.width;
			canvas.height = img.height;
			const ctx = canvas.getContext('2d');

			if (ctx) {
				ctx.drawImage(img, 0, 0, img.width, img.height);
				canvas.toBlob(
					(blob) => {
						if (blob) {
							const url = URL.createObjectURL(blob);
							const a = document.createElement('a');
							a.href = url;
							a.download = `task_image_${Date.now()}.jpg`;
							document.body.appendChild(a);
							a.click();
							document.body.removeChild(a);
							URL.revokeObjectURL(url);
							showToast('图片已准备下载', 'success');
						} else {
							showToast('图片处理失败', 'error');
						}
						uni.hideLoading();
					},
					'image/jpeg',
					0.9
				);
			} else {
				showToast('浏览器不支持Canvas', 'error');
				uni.hideLoading();
			}
		};

		img.onerror = () => {
			console.error('图片加载失败');
			showToast('图片加载失败', 'error');
			uni.hideLoading();
		};

		img.src = image.imageUrl;
	};

	try {
		downloadImageForH5();
	} catch (err) {
		console.error('下载失败:', err);
		showToast('下载失败', 'error');
		uni.hideLoading();
	}
	// #endif
};

// 处理删除图片
const handleDeleteImage = (image: any) => {
	uni.showModal({
		title: '确认删除',
		content: '确定要删除这张图片吗？',
		confirmText: '确定',
		cancelText: '取消',
		success: (res: any) => {
			if (res.confirm) {
				if (deleteLocalImage(image.imageUrl)) {
					showToast('删除成功', 'success');
					// 清除缓存，强制重新计算
					clearCache();
				} else {
					showToast('删除失败', 'error');
				}
			}
		},
	});
};

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 检查是否可以添加照片
const canAddPhoto = (): boolean => {
	if (!task.value?.taskId) {
		showToast('任务数据无效，无法拍照', 'error');
		return false;
	}

	const stats = currentTaskStats.value;
	const totalTaken = stats.hasTaken + stats.pendingUpload; // 已传 + 待传

	if (totalTaken >= stats.shouldTake) {
		showToast(`已达到最大拍照数量限制（${totalTaken}/${stats.shouldTake}）`, 'none');
		return false;
	}

	return true;
};

// 通用的图片处理函数
const handleImageCapture = async (captureMethod: () => Promise<string>, errorMessage: string) => {
	if (!canAddPhoto()) return;

	try {
		const tempImageUrl = await captureMethod();
		await processNewImage(tempImageUrl);
	} catch (error) {
		console.error(`${errorMessage}:`, error);
		showToast(errorMessage);
	}
};

// 处理拍照
const handleTakePhoto = () => handleImageCapture(TaskUtils.takePhoto, '拍照失败');

// 处理从图库选择
const handleChooseFromAlbum = () => handleImageCapture(TaskUtils.chooseFromAlbum, '选择图片失败');

// 清除缓存
const clearCache = () => {
	taskDataCache.value.lastUpdateTime = 0;
	deliveryInfoCache.value.taskId = '';
	console.info('📄 [TASK-DETAIL] 缓存已清除');
};

// 处理新图片（保存到本地并添加到任务图片列表）
const processNewImage = async (tempImageUrl: string) => {
	try {
		// 先保存图片到本地，获取永久路径
		const imageUrl = await imagesStore.saveImageToLocal(tempImageUrl);

		// 获取位置信息
		let location: { latitude: number; longitude: number } | undefined;
		try {
			location = await TaskUtils.getLocation();
		} catch (error) {
			console.error('获取位置信息失败:', error);
			// 位置获取失败时不影响拍照功能继续进行
			location = undefined;
		}

		// 创建完整的任务图片对象
		const newTaskImage = TaskUtils.createTaskImage({
			zoneId: task.value.zoneId,
			taskId: task.value.taskId,
			spotId: task.value.spotId,
			spotCode: task.value.spotCode,
			imageUrl, // 使用永久路径
			location,
			task: task.value,
		});

		// 添加到本地图片存储
		await addLocalImage(task.value, newTaskImage);
		showToast('照片已保存到本地', 'success');

		// 清除缓存，强制重新计算
		clearCache();
	} catch (error) {
		console.error('处理图片失败:', error);
		showToast('处理图片失败');
	}
};

// 注意：loadTaskImages 函数已被移除，现在使用计算属性 currentTaskLocalPendingImages 和 currentTaskStats

// 获取状态栏高度
const getStatusBarHeight = () => {
	try {
		// 获取系统信息
		const systemInfo = uni.getSystemInfoSync();
		// 设置状态栏高度
		statusBarHeight.value = systemInfo.statusBarHeight || 20;

		return statusBarHeight.value;
	} catch (error) {
		console.error('获取状态栏高度失败:', error);
		statusBarHeight.value = 20; // 设置默认值
		return statusBarHeight.value;
	}
};

// 页面加载时获取任务信息
onMounted(() => {
	// 获取状态栏高度
	getStatusBarHeight();

	// 初始化字体大小
	initFontSize();

	// 监听字体大小变化
	uni.$on('fontSizeChanged', handleFontSizeChange);

	// 获取页面参数
	const eventChannel = getOpenerEventChannel();
	eventChannel.on('taskDetail', (data: any) => {
		// 检查数据有效性
		if (!data) {
			showToast('未接收到任务数据', 'error');
			return;
		}

		// 如果传递了任务对象，直接使用
		if (data.task) {
			task.value = data.task;
			taskId.value = data.task.taskId;
		} else {
			showToast('无效的任务数据', 'error');
			setTimeout(() => {
				goBack();
			}, 1500);
			return;
		}

		// 检查任务对象是否有效
		if (!task.value) {
			showToast('任务数据无效', 'error');
			setTimeout(() => {
				goBack();
			}, 1500);
			return;
		}

		// 立即设置基本信息，让页面快速显示
		console.info('📄 [TASK-DETAIL] 任务数据已设置，开始异步处理');

		// 异步处理派单信息，避免阻塞页面渲染
		nextTick(() => {
			processDeliveryInfo();

			// 设置图片加载状态
			if (task.value?.previousContentImageUrl) {
				imageState.loading.previous = true;
			}
			if (task.value?.currentContentImageUrl) {
				imageState.loading.current = true;
			}

			console.info('📄 [TASK-DETAIL] 页面初始化完成');
		});
	});
});

onUnmounted(() => {
	// 清理事件监听
	uni.$off('fontSizeChanged', handleFontSizeChange);
});

// 获取事件通道
const getOpenerEventChannel = () => {
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	return currentPage.getOpenerEventChannel();
};
</script>

<style lang="scss">
.task-detail-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative; /* 添加相对定位，用于底部按钮的绝对定位 */

	// 应用字体缩放到文本元素
	.section-label, .section-value, .delivery-item, .stats-label, .stats-value,
	.completion-text, .empty-image-text, .empty-photos, .photo-time-label,
	.photo-time, .button-text, .navbar-title, .back-icon {
		font-size: calc(1em * var(--font-scale, 1)) !important;
		line-height: calc(1.4 * var(--font-scale, 1)) !important;
	}

	.navbar {
		/* 使用固定高度和内边距，确保在所有设备上显示一致 */
		height: 44px;
		/* 状态栏高度通过内联样式设置 */
		background-color: #2196f3; // 蓝色导航栏
		color: #fff;
		display: flex;
		align-items: center;
		padding-left: 15px;
		padding-right: 15px;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;

		.navbar-left {
			width: 60px;
			display: flex;
			align-items: center;

			.back-icon {
				font-size: calc(var(--font-size-lg) * 1.2);
				font-weight: bold;
			}
		}

		.navbar-center {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;

			.task-type-badge {
				display: flex;
				align-items: center;
				padding: 6px 12px;
				border-radius: 20px;
				font-size: 14px;
				font-weight: 700;
				border: 2px solid;

				.task-type-icon {
					margin-right: 6px;
					font-size: 16px;
				}

				.task-type-text {
					font-weight: 800;
					letter-spacing: 0.5px;
				}

				// 实景任务样式
				&.zone-task {
					background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
					border-color: #4caf50;
					color: #2e7d32;
					box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
				}

				// 点位任务样式
				&.spot-task {
					background: linear-gradient(135deg, #e3f2fd, #bbdefb);
					border-color: #2196f3;
					color: #1565c0;
					box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4);
				}
			}
		}

		.navbar-title {
			flex: 1;
			text-align: center;
			font-size: var(--font-size-lg);
			font-weight: bold;
		}

		.navbar-right {
			width: 80px;
			display: flex;
			justify-content: flex-end;

			.photo-text {
				font-size: var(--font-size-base);
				color: #fff;
			}

			.photo-icon {
				font-size: 16px;
				color: #fff;
				font-weight: bold;
			}
		}
	}

	.task-detail-content {
		/* 适应状态栏高度和导航栏高度 - 通过内联样式设置 */
		padding-top: 10px; /* 添加额外的顶部内边距 */
		flex: 1;

		.detail-section {
			background-color: #fff;
			padding: 12px 15px;
			border-bottom: 1px solid #eee;

			&:last-child {
				border-bottom: none;
				margin-bottom: 20px;
			}

			.section-label {
				font-size: var(--font-size-base);
				color: #666;
				margin-bottom: 8px;
				font-weight: 700;
			}

			.section-value {
				font-size: var(--font-size-lg);
				color: #333;
				line-height: 1.4;
				font-weight: 500;
			}

			&.address-section {
				padding-top: 0;

				.address-value {
					color: #666;
					font-size: var(--font-size-base);
					font-weight: 500;
				}
			}

			&.image-section {
				padding: 15px;

				.image-label {
					font-weight: 700; /* 增加粗细 */
					font-size: var(--font-size-base);

					&.previous-label {
						color: #ff5252; // 红色
					}

					&.current-label {
						color: #4caf50; // 绿色
					}
				}

				&.side-by-side {
					display: flex;
					flex-direction: row;
					justify-content: space-between;

					.image-column {
						width: 48%; // 留一点间距

						.image-container {
							height: 180px; // 稍微减小高度以适应并排显示
						}
					}
				}

				.image-container {
					margin-top: 10px;
					width: 100%;
					height: 200px;
					background-color: #f8f8f8;
					display: flex;
					align-items: center;
					justify-content: center;
					overflow: hidden;

					.content-image {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}

					.empty-image {
						width: 100%;
						height: 100%;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #f0f0f0;

						.empty-image-text {
							color: #999;
							font-size: var(--font-size-base);
							font-weight: 500;
						}
					}
				}
			}

			.delivery-list {
				margin-top: 5px;

				.delivery-item {
					padding: 8px 0;
					border-bottom: 1px solid #f0f0f0;
					font-size: var(--font-size-base);
					color: #333;
					font-weight: 500;

					&:last-child {
						border-bottom: none;
					}
				}
			}

			.stats-info {
				display: flex;
				flex-wrap: wrap;
				gap: 15px;
				margin-top: 5px;

				.stats-item {
					display: flex;
					align-items: center;
					background-color: #f8f9fa;
					padding: 8px 12px;
					border-radius: 6px;
					border-left: 3px solid #e9ecef;

					.stats-label {
						font-size: var(--font-size-sm);
						color: #666;
						margin-right: 4px;
					}

					.stats-value {
						font-size: var(--font-size-base);
						color: #333;
						font-weight: 700;

						&.pending {
							color: #dd524d; /* 红色 */
						}

						&.uploaded {
							color: #28a745; /* 绿色 */
						}
					}

					// 突出显示的标签和数值
					.highlight-label {
						font-weight: 800 !important;
						color: #495057 !important;
						font-size: calc(var(--font-size-sm) * 1.1) !important;
					}

					.highlight-value {
						font-weight: 900 !important;
						font-size: calc(var(--font-size-base) * 1.2) !important;
						text-shadow: 0 1px 2px rgba(0,0,0,0.1);

						&.pending {
							color: #dc3545 !important; /* 更鲜艳的红色 */
							background-color: rgba(220, 53, 69, 0.1);
							padding: 2px 6px;
							border-radius: 4px;
						}

						&.uploaded {
							color: #198754 !important; /* 更鲜艳的绿色 */
							background-color: rgba(25, 135, 84, 0.1);
							padding: 2px 6px;
							border-radius: 4px;
						}

						&:not(.pending):not(.uploaded) {
							color: #0d6efd !important; /* 蓝色用于应拍数量 */
							background-color: rgba(13, 110, 253, 0.1);
							padding: 2px 6px;
							border-radius: 4px;
						}
					}
				}
			}

			// 突出显示统计标题
			.stats-section-label {
				font-weight: 800 !important;
				color: #212529 !important;
				font-size: calc(var(--font-size-base) * 1.1) !important;
				background-color: #e9ecef;
				padding: 4px 8px;
				border-radius: 4px;
				display: inline-block;
			}

			.completion-status {
				margin-top: 10px;
				padding-top: 10px;
				border-top: 1px solid #f0f0f0;
				text-align: center;

				.completion-text {
					font-size: var(--font-size-base);
					font-weight: 600;
					color: #666;

					&.completed {
						color: #4caf50;
					}
				}

				// 突出显示完成状态
				.highlight-status {
					font-weight: 800 !important;
					font-size: calc(var(--font-size-base) * 1.3) !important;
					padding: 8px 16px;
					border-radius: 20px;
					display: inline-block;
					text-shadow: 0 1px 2px rgba(0,0,0,0.1);

					&.completed {
						color: #ffffff !important;
						background: linear-gradient(135deg, #28a745, #20c997);
						box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
					}

					&:not(.completed) {
						color: #856404 !important;
						background: linear-gradient(135deg, #fff3cd, #ffeaa7);
						border: 2px solid #ffc107;
						box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
					}
				}
			}

			.photo-list {
				margin-top: 10px;

				/* 待上传照片列表样式 */
				.pending-photo-list {
					width: 100%;
				}

				.pending-photo-item {
					display: flex;
					align-items: center;
					padding: 24rpx 0;
					border-bottom: 1rpx solid #f0f0f0;
					background: #fff;
					transition: background-color 0.2s;
				}

				.pending-photo-item:active {
					background: #f5f5f5;
				}

				.pending-photo-item:last-child {
					border-bottom: none;
				}

				.photo-thumbnail {
					width: 120rpx;
					height: 120rpx;
					border-radius: 8rpx;
					overflow: hidden;
					margin-right: 24rpx;
					flex-shrink: 0;
				}

				.photo-thumbnail image {
					width: 100%;
					height: 100%;
				}

				.photo-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;
				}

				.photo-time-label {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 8rpx;
				}

				.photo-time {
					font-size: 32rpx;
					color: #666;
					line-height: 1.2;
				}

				.arrow-icon {
					width: 40rpx;
					height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #ccc;
					font-size: 16px;
					margin-left: 16rpx;
				}

				.photo-grid {
					display: flex;
					flex-wrap: wrap;
					margin: 0 -5px;

					.photo-item {
						width: 33.33%;
						padding: 5px;
						box-sizing: border-box;
						position: relative;

						.photo-wrapper {
							position: relative;
							width: 100%;
							height: 100px;
							border-radius: 2px;
							overflow: hidden;
						}

						image {
							width: 100%;
							height: 100px;
							object-fit: cover;
							border-radius: 2px;
						}

						.photo-actions {
							position: absolute;
							bottom: 0;
							left: 0;
							right: 0;
							background-color: rgba(0, 0, 0, 0.5);
							display: flex;
							justify-content: space-around;
							padding: 4px 0;
						}

						.action-btn {
							background: none;
							border: none;
							color: white;
							font-size: 12px;
							padding: 2px 4px;
							margin: 0;
							line-height: 1;
							height: auto;
							flex: 1;
							text-align: center;
						}

						.action-btn.view {
							color: #1890ff; /* 蓝色 */
						}

						.action-btn.save {
							color: #52c41a; /* 绿色 */
						}

						.action-btn.delete {
							color: #ff4d4f; /* 红色 */
						}
					}
				}

				.empty-photos {
					padding: 20px 0;
					text-align: center;
					color: #999;
					font-size: var(--font-size-base);
					font-weight: 500;
				}
			}

			// 历史实景图片样式
			.historical-images-grid {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;
				margin-top: 8px;
			}

			.historical-image-item {
				width: calc(33.333% - 6px);
				aspect-ratio: 1;
				position: relative;
				border-radius: 6px;
				overflow: hidden;
				border: 1px solid #e0e0e0;
				cursor: pointer;
				transition: transform 0.2s ease;

				&:active {
					transform: scale(0.95);
				}
			}

			.historical-image-container {
				width: 100%;
				height: 100%;
				position: relative;
			}

			.historical-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.historical-image-overlay {
				position: absolute;
				top: 4px;
				right: 4px;
				background-color: rgba(0, 0, 0, 0.6);
				color: white;
				padding: 2px 6px;
				border-radius: 10px;
				font-size: 12px;
				line-height: 1;
			}

			.historical-image-index {
				font-size: 10px;
				font-weight: bold;
			}

			.empty-historical {
				text-align: center;
				padding: 40px 20px;
				color: #999;
			}

			.empty-historical-text {
				font-size: 14px;
				color: #999;
			}
		}
	}

	/* 底部固定按钮样式 */
	.footer-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		height: 60px;
		background-color: #fff;
		border-top: 1px solid #eee;
		z-index: 100;

		.footer-button {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 8px 0;
			cursor: pointer;
			transition: background-color 0.2s ease;

			&:active {
				background-color: #f5f5f5;
			}

			.button-icon {
				font-size: 16px;
				margin-bottom: 4px;
			}

			.button-text {
				font-size: 14px;
				color: #333;
				font-weight: 500;
			}
		}

		.camera-button {
			border-right: 1px solid #eee;

			.button-icon {
				color: #4caf50; /* 绿色 */
			}

			&.single-button {
				border-right: none;
			}
		}

		.album-button {
			.button-icon {
				color: #2196f3; /* 蓝色 */
			}
		}
	}
}
</style>
