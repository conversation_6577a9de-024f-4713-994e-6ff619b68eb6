<template>
	<view class="login-container">
		<view class="login-header">
			<image class="banner" src="/static/login-banner.svg" mode="widthFix"></image>
		</view>

		<view class="login-form">
			<view class="input-group">
				<input class="input" type="text" v-model="username" placeholder="请输入用户名" />
			</view>
			<view class="input-group">
				<input class="input" type="password" v-model="password" placeholder="请输入密码" />
			</view>
			<button class="login-btn" @click="handleLogin" :loading="loading">
				{{ loading ? '登录中...' : '登录' }}
			</button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { useAuthStore } from '@/stores';
	import { showToast } from '@/utils';

	const username = ref('');
	const password = ref('');
	const loading = ref(false);

	const authStore = useAuthStore();

	const handleLogin = async () => {
		if (!username.value || !password.value) {
			showToast('请输入用户名和密码', 'none');
			return;
		}

		loading.value = true;
		try {
			const success = await authStore.login({
				username: username.value,
				password: password.value
			});

			if (success) {
				// 登录成功后跳转到派单页面
				// 注意：authStore.login 已经加载了用户数据，不需要再次加载
				uni.reLaunch({
					url: '/pages/delivery/index'
				});
			} else {
				showToast('登录失败，请检查用户名和密码', 'none');
			}
		} catch (error) {
			showToast('登录失败，请稍后重试', 'none');
		} finally {
			loading.value = false;
		}
	};
</script>

<style lang="scss">
	.login-container {
		min-height: 100vh;
		padding: 80rpx 40rpx;
		background-color: $uni-bg-color-grey;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.login-header {
		margin-top: 60rpx;
		margin-bottom: 60rpx;
		width: 100%;

		.banner {
			width: 100%;
			border-radius: 15rpx;
			box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.1);
		}
	}

	.login-form {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;

		.input-group {
			width: 98%;
			margin-bottom: 30rpx;

			.input {
				width: 100%;
				height: 90rpx;
				background: $uni-bg-color;
				border-radius: 45rpx;
				padding: 0 40rpx;
				font-size: 28rpx;
				box-sizing: border-box;
				border: 2rpx solid $uni-border-color;
				color: $uni-text-color;

				&::placeholder {
					color: $uni-text-color-placeholder;
				}
			}
		}

		.login-btn {
			width: 98%;
			height: 90rpx;
			line-height: 90rpx;
			background: $uni-color-primary;
			color: $uni-text-color-inverse;
			border-radius: 45rpx;
			font-size: 32rpx;
			margin-top: 60rpx;

			&:active {
				opacity: 0.8;
			}
		}
	}
</style>