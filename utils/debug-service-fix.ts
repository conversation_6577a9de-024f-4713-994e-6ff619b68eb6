import { ENVIRONMENT } from './env';

// 声明全局对象
declare const uni: any;
declare const plus: any;
declare const window: any;

/**
 * 修复uni-app调试服务连接问题
 */
export class DebugServiceFix {
  private static initialized = false;

  /**
   * 初始化调试服务修复
   */
  static initialize(): void {
    if (this.initialized) {
      return;
    }

    try {
      // 生产环境下禁用调试服务
      if (ENVIRONMENT.isProduction) {
        this.disableDebugService();
      }

      // 处理调试服务相关错误
      this.handleDebugServiceErrors();

      this.initialized = true;

    } catch (error) {
      console.error('调试服务修复初始化失败:', error);
    }
  }

  /**
   * 禁用调试服务
   */
  private static disableDebugService(): void {
    try {
      // 禁用HBuilderX调试服务连接
      if (typeof window !== 'undefined') {
        // 阻止调试服务相关的网络请求
        const originalFetch = window.fetch;
        if (originalFetch) {
          window.fetch = function(url: string, ...args: any[]) {
            // 检查是否是调试服务相关的请求
            if (typeof url === 'string' && (
              url.includes('192.168.') ||
              url.includes('localhost') ||
              url.includes(':8080') ||
              url.includes('debug') ||
              url.includes('devtools')
            )) {
              // 返回一个rejected的Promise
              return Promise.reject(new Error('调试服务已在生产环境中禁用'));
            }
            return originalFetch.apply(this, [url, ...args]);
          };
        }

        // 阻止WebSocket连接到调试服务
        const originalWebSocket = window.WebSocket;
        if (originalWebSocket) {
          window.WebSocket = function(url: string, ...args: any[]) {
            if (typeof url === 'string' && (
              url.includes('192.168.') ||
              url.includes('localhost') ||
              url.includes('debug') ||
              url.includes('devtools')
            )) {
              throw new Error('调试服务WebSocket已在生产环境中禁用');
            }
            return new originalWebSocket(url, ...args);
          };
        }
      }

      // 禁用uni-app的调试相关功能
      if (typeof uni !== 'undefined') {
        // 重写可能触发调试服务连接的方法
        const debugMethods = [
          'getSystemInfo',
          'getNetworkType',
          'getLocation'
        ];

        debugMethods.forEach(method => {
          if (uni[method]) {
            const originalMethod = uni[method];
            uni[method] = function(options: any = {}) {
              // 移除可能的调试参数
              const cleanOptions = { ...options };
              delete cleanOptions.debug;
              delete cleanOptions.devtools;

              return originalMethod.call(this, cleanOptions);
            };
          }
        });
      }

    } catch (error) {
      console.error('禁用调试服务失败:', error);
    }
  }

  /**
   * 处理调试服务相关错误
   */
  private static handleDebugServiceErrors(): void {
    // 捕获并静默处理调试服务相关错误
    const originalConsoleError = console.error;

    console.error = function(...args: any[]) {
      const message = args.join(' ');

      // 检查是否是调试服务相关错误
      if (typeof message === 'string' && (
        message.includes('局域网地址') ||
        message.includes('本地调试服务') ||
        message.includes('__WEEX_CALL_JAVASCRIPT__') ||
        message.includes('Failed to execute the callback function') ||
        message.includes('JavaScript execute error') ||
        message.includes('debug') && message.includes('不可用')
      )) {
        // 在生产环境中静默处理这些错误
        if (ENVIRONMENT.isProduction) {
          return;
        }
      }

      // 其他错误正常输出
      originalConsoleError.apply(console, args);
    };

    // 处理uni-app框架错误
    if (typeof uni !== 'undefined') {
      // 监听应用错误
      uni.onError?.((error: string) => {
        if (error.includes('局域网地址') ||
            error.includes('本地调试服务') ||
            error.includes('__WEEX_CALL_JAVASCRIPT__')) {
          // 静默处理调试服务相关错误
          return;
        }

        // 其他错误正常处理
        console.error('应用错误:', error);
      });

      // 监听页面错误
      uni.onPageNotFound?.((res: any) => {
        console.error('页面未找到:', res);
      });
    }

    // 处理plus环境的错误
    if (typeof plus !== 'undefined' && typeof document !== 'undefined') {
      // 监听plus错误
      try {
        document.addEventListener('plusready', () => {
          if (plus.runtime) {
            plus.runtime.getProperty?.(plus.runtime.appid, (info: any) => {
              // 在生产环境中禁用调试模式
              if (ENVIRONMENT.isProduction && info.debug) {
                console.warn('检测到调试模式，建议在生产环境中关闭');
              }
            });
          }
        });
      } catch (error) {
        // 静默处理 document 不可用的情况
      }
    }
  }

  /**
   * 检查并修复网络连接问题
   */
  static checkNetworkConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      if (typeof uni === 'undefined') {
        resolve(true);
        return;
      }

      uni.getNetworkType({
        success: (res: any) => {
          const isConnected = res.networkType !== 'none';
          if (!isConnected) {
            console.warn('网络连接不可用');
          }
          resolve(isConnected);
        },
        fail: () => {
          console.warn('无法获取网络状态');
          resolve(false);
        }
      });
    });
  }

  /**
   * 清理调试相关的存储数据
   */
  static cleanupDebugData(): void {
    if (typeof uni === 'undefined' || !ENVIRONMENT.isProduction) {
      return;
    }

    const debugKeys = [
      'debug_info',
      'dev_server_url',
      'local_debug_port',
      'hbuilderx_debug',
      'weex_debug',
      'devtools_config'
    ];

    debugKeys.forEach(key => {
      try {
        uni.removeStorageSync(key);
      } catch (error) {
        // 静默处理
      }
    });
  }

  /**
   * 获取修复状态
   */
  static getStatus(): any {
    return {
      initialized: this.initialized,
      environment: ENVIRONMENT.current,
      isProduction: ENVIRONMENT.isProduction,
      timestamp: new Date().toISOString()
    };
  }
}

// 自动初始化
DebugServiceFix.initialize();

// 清理调试数据
DebugServiceFix.cleanupDebugData();
