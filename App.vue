<script>
	import { useAuthStore } from '@/stores/auth';

	export default {
		data() {
			return {
				currentFontScale: 1
			};
		},

		onLaunch: async function() {
			// 检查是否已登录
			const authStore = useAuthStore();
			if (await authStore.checkAuth()) {
				// 已登录则直接跳转到派单页面
				uni.reLaunch({
					url: '/pages/delivery/index'
				});
			}

			// 设置状态栏高度CSS变量
			try {
				const sysInfo = uni.getSystemInfoSync();
				if (sysInfo && sysInfo.statusBarHeight) {
					// 在uni-app中，通过uni.setStorageSync保存状态栏高度
					uni.setStorageSync('statusBarHeight', sysInfo.statusBarHeight);

					// 仅在H5环境中设置CSS变量
					// #ifdef H5
					if (typeof document !== 'undefined' && document.documentElement) {
						document.documentElement.style.setProperty('--status-bar-height', `${sysInfo.statusBarHeight}px`);
					}
					// #endif
				}
			} catch (e) {
				console.error('设置状态栏高度失败:', e);
			}

			// 初始化字体大小设置
			this.initFontSize();

			// 注意：状态加载已移至各个页面的onShow或onMounted钩子中
		},

		// 应用从后台恢复时触发
		onShow: function() {
			// 初始化字体大小设置
			this.initFontSize();

			// 注意：状态加载已移至各个页面的onShow或onMounted钩子中
		},

		methods: {

			// 初始化字体大小设置
			initFontSize() {
				try {
					const savedFontSize = uni.getStorageSync('appFontSize') || 'normal';
					const savedFontScale = uni.getStorageSync('fontScale') || 1;

					// H5环境中设置CSS类
					// #ifdef H5
					if (typeof document !== 'undefined' && document.documentElement) {
						// 移除所有字体大小类
						document.documentElement.classList.remove('font-size-normal', 'font-size-large', 'font-size-xlarge');
						// 应用保存的字体大小
						document.documentElement.classList.add(`font-size-${savedFontSize}`);
						// 设置CSS变量
						document.documentElement.style.setProperty('--font-scale', savedFontScale.toString());
					}
					// #endif

					// 移动端环境中设置CSS变量
					// #ifndef H5
					// 在移动端，我们通过CSS变量来控制字体大小
					// 这个变量会在页面的CSS中被使用
					this.currentFontScale = savedFontScale;

					// 设置全局CSS变量
					try {
						// 在移动端，我们需要确保CSS变量能够正确传递到页面
						// 通过设置根元素的CSS变量
						if (typeof document !== 'undefined' && document.documentElement) {
							document.documentElement.style.setProperty('--font-scale', savedFontScale.toString());
						}
					} catch (error) {
						console.warn('设置移动端CSS变量失败:', error);
					}

					console.info(`📱 [FONT] 移动端字体缩放初始化: ${savedFontSize} (${savedFontScale})`);
					// #endif

					console.info(`🎨 [FONT] 字体大小初始化完成: ${savedFontSize} (缩放: ${savedFontScale})`);
				} catch (e) {
					console.error('初始化字体大小设置失败:', e);
				}
			}
		}
	}
</script>

<style>
	/* 引入图标样式 */
	@import url("/static/css/icons.css");

	/*每个页面公共css */
	:root {
		/* 基础字体大小变量 */
		--base-font-size: 1;
		/* 字体大小缩放变量 */
		--font-scale: var(--base-font-size);
		/* 过渡效果 */
		--font-transition: font-size 0.3s ease;
		/* 状态栏高度，默认值，会在App.vue的onLaunch中被覆盖 */
		--status-bar-height: 20px;
	}

	/* 全局字体大小设置 */
	page {
		/* 设置基础字体大小 */
		font-size: calc(16px * var(--font-scale));
		/* 增加全局字体粗细 */
		font-weight: 500;
	}

	/* 移动端字体大小设置 */
	/* #ifndef H5 */
	page {
		/* 在移动端，我们需要确保CSS变量能够正确应用 */
		font-size: calc(16px * var(--font-scale, 1));
	}
	/* #endif */

	/* 字体大小类 */
	.font-size-normal {
		--font-scale: 1;
	}

	.font-size-large {
		--font-scale: 1.2;
	}

	.font-size-xlarge {
		--font-scale: 1.4;
	}

	/* 添加平滑过渡效果 */
	* {
		transition: var(--font-transition);
	}
</style>