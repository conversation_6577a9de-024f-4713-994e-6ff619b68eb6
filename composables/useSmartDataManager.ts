import { ref, computed, onMounted, onUnmounted } from 'vue';
import { smartDataManager } from '@/utils/SmartDataManager';
import { performanceMonitor } from '@/utils/PerformanceMonitor';
import { cacheManager } from '@/utils/CacheManager';
import type { DataManagerState } from '@/utils/SmartDataManager';
import type { PerformanceStats } from '@/utils/PerformanceMonitor';
import type { CacheStats } from '@/utils/CacheManager';

/**
 * 智能数据管理系统的组合式函数
 * 提供统一的数据管理接口和状态监控
 */
export const useSmartDataManager = () => {
	// 响应式状态
	const managerState = ref<DataManagerState>(smartDataManager.getState());
	const performanceStats = ref<PerformanceStats | null>(null);
	const cacheStats = ref<CacheStats | null>(null);
	const isInitialized = ref(false);

	// 性能监控定时器
	let statsUpdateTimer: any = null;

	/**
	 * 初始化智能数据管理系统
	 */
	const initialize = async () => {
		if (isInitialized.value) {
			console.warn('⚠️ [SMART DATA MANAGER] 已经初始化，跳过重复初始化');
			return;
		}

		console.info('🚀 [SMART DATA MANAGER] 初始化智能数据管理系统');

		try {
			// 更新状态
			managerState.value = smartDataManager.getState();

			// 启动性能监控
			startPerformanceMonitoring();

			isInitialized.value = true;
			console.info('✅ [SMART DATA MANAGER] 智能数据管理系统初始化完成');
		} catch (error) {
			console.error('❌ [SMART DATA MANAGER] 初始化失败:', error);
			throw error;
		}
	};

	/**
	 * 检查并加载数据
	 */
	const checkAndLoadData = async (
		currentUser: string,
		loadDeliveries: () => Promise<void>,
		loadTasks: () => Promise<void>,
		onProgress?: (stage: string, progress: number) => void
	): Promise<boolean> => {
		console.info('🔍 [SMART DATA MANAGER] 检查数据加载需求');

		const needsLoad = await performanceMonitor.measure(
			'check-and-load-data',
			'data-loading',
			async () => {
				return await smartDataManager.checkAndLoadData(
					currentUser,
					loadDeliveries,
					loadTasks,
					onProgress
				);
			},
			{ user: currentUser }
		);

		// 更新状态
		managerState.value = smartDataManager.getState();

		return needsLoad;
	};

	/**
	 * 强制加载数据
	 */
	const forceLoadData = async (
		currentUser: string,
		loadDeliveries: () => Promise<void>,
		loadTasks: () => Promise<void>,
		onProgress?: (stage: string, progress: number) => void
	): Promise<void> => {
		console.info('🚀 [SMART DATA MANAGER] 强制加载数据');

		await performanceMonitor.measure(
			'force-load-data',
			'data-loading',
			async () => {
				await smartDataManager.forceLoadData(
					currentUser,
					loadDeliveries,
					loadTasks,
					onProgress
				);
			},
			{ user: currentUser }
		);

		// 更新状态
		managerState.value = smartDataManager.getState();
	};

	/**
	 * 执行数据分析
	 */
	const performDataAnalysis = async (tasks: any[], deliveries: any[]) => {
		console.info('🔍 [SMART DATA MANAGER] 执行数据分析');

		const analysisResult = await performanceMonitor.measure(
			'data-analysis',
			'computation',
			async () => {
				return await smartDataManager.performDataAnalysis(tasks, deliveries);
			},
			{ taskCount: tasks.length, deliveryCount: deliveries.length }
		);

		// 更新状态
		managerState.value = smartDataManager.getState();

		return analysisResult;
	};

	/**
	 * 更新任务状态
	 */
	const updateTaskStatus = async (taskId: string, statusUpdate: any) => {
		console.info(`🔄 [SMART DATA MANAGER] 更新任务状态: ${taskId}`);

		await performanceMonitor.measure(
			'update-task-status',
			'user-interaction',
			async () => {
				await smartDataManager.updateTaskStatus(taskId, statusUpdate);
			},
			{ taskId }
		);

		// 更新状态
		managerState.value = smartDataManager.getState();
	};

	/**
	 * 批量更新任务状态
	 */
	const batchUpdateTaskStatus = async (updates: any[]) => {
		console.info(`🔄 [SMART DATA MANAGER] 批量更新任务状态，数量: ${updates.length}`);

		await performanceMonitor.measure(
			'batch-update-task-status',
			'computation',
			async () => {
				await smartDataManager.batchUpdateTaskStatus(updates);
			},
			{ updateCount: updates.length }
		);

		// 更新状态
		managerState.value = smartDataManager.getState();
	};

	/**
	 * 获取缓存数据
	 */
	const getCachedData = <T>(key: string): T | null => {
		return smartDataManager.getCachedData<T>(key);
	};

	/**
	 * 设置缓存数据
	 */
	const setCachedData = <T>(key: string, data: T, options?: any) => {
		smartDataManager.setCachedData(key, data, options);
	};

	/**
	 * 失效相关缓存
	 */
	const invalidateCache = (dependency: string) => {
		smartDataManager.invalidateCache(dependency);
	};

	/**
	 * 清除所有数据和缓存
	 */
	const clearAllData = () => {
		console.info('🗑️ [SMART DATA MANAGER] 清除所有数据和缓存');
		smartDataManager.clearAllData();
		managerState.value = smartDataManager.getState();
	};

	/**
	 * 启动性能监控
	 */
	const startPerformanceMonitoring = () => {
		// 定期更新性能统计
		statsUpdateTimer = setInterval(() => {
			updateStats();
		}, 5000); // 每5秒更新一次

		// 立即更新一次
		updateStats();

		console.info('📊 [SMART DATA MANAGER] 性能监控已启动');
	};

	/**
	 * 停止性能监控
	 */
	const stopPerformanceMonitoring = () => {
		if (statsUpdateTimer) {
			clearInterval(statsUpdateTimer);
			statsUpdateTimer = null;
		}
		console.info('📊 [SMART DATA MANAGER] 性能监控已停止');
	};

	/**
	 * 更新统计信息
	 */
	const updateStats = () => {
		try {
			performanceStats.value = smartDataManager.getPerformanceStats();
			cacheStats.value = smartDataManager.getCacheStats();
		} catch (error) {
			console.warn('⚠️ [SMART DATA MANAGER] 更新统计信息失败:', error);
		}
	};

	/**
	 * 生成性能报告
	 */
	const generatePerformanceReport = (): string => {
		return smartDataManager.generatePerformanceReport();
	};

	// 计算属性
	const isLoading = computed(() => managerState.value.isLoading);
	const lastLoadTime = computed(() => managerState.value.lastLoadTime);
	const lastLoadUser = computed(() => managerState.value.lastLoadUser);
	const dataVersion = computed(() => managerState.value.dataVersion);
	const analysisResult = computed(() => managerState.value.analysisResult);

	// 性能指标计算属性
	const averageLoadTime = computed(() => {
		try {
			if (!performanceStats.value || !performanceStats.value.byCategory) return 0;
			const dataLoadingStats = performanceStats.value.byCategory['data-loading'];
			return dataLoadingStats ? (dataLoadingStats.averageDuration || 0) : 0;
		} catch (error) {
			console.warn('⚠️ [SMART DATA MANAGER] 计算平均加载时间失败:', error);
			return 0;
		}
	});

	const cacheHitRate = computed(() => {
		try {
			if (!cacheStats.value || typeof cacheStats.value.hitRate !== 'number') return 0;
			return cacheStats.value.hitRate * 100;
		} catch (error) {
			console.warn('⚠️ [SMART DATA MANAGER] 计算缓存命中率失败:', error);
			return 0;
		}
	});

	const memoryUsage = computed(() => {
		try {
			if (!performanceStats.value?.memoryUsage) return null;
			const memory = performanceStats.value.memoryUsage;
			if (typeof memory.used !== 'number' || typeof memory.total !== 'number') return null;
			return {
				used: (memory.used / 1024 / 1024).toFixed(2),
				total: (memory.total / 1024 / 1024).toFixed(2),
				percentage: (memory.percentage || 0).toFixed(1)
			};
		} catch (error) {
			console.warn('⚠️ [SMART DATA MANAGER] 计算内存使用失败:', error);
			return null;
		}
	});

	// 生命周期钩子
	onMounted(() => {
		initialize();
	});

	onUnmounted(() => {
		stopPerformanceMonitoring();
	});

	return {
		// 状态
		managerState: computed(() => managerState.value),
		performanceStats: computed(() => performanceStats.value),
		cacheStats: computed(() => cacheStats.value),
		isInitialized: computed(() => isInitialized.value),

		// 计算属性
		isLoading,
		lastLoadTime,
		lastLoadUser,
		dataVersion,
		analysisResult,
		averageLoadTime,
		cacheHitRate,
		memoryUsage,

		// 方法
		initialize,
		checkAndLoadData,
		forceLoadData,
		performDataAnalysis,
		updateTaskStatus,
		batchUpdateTaskStatus,
		getCachedData,
		setCachedData,
		invalidateCache,
		clearAllData,
		generatePerformanceReport,
		updateStats,

		// 监控控制
		startPerformanceMonitoring,
		stopPerformanceMonitoring
	};
};
