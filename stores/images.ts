import { ref, computed } from 'vue';
import { _UnwrapAll, defineStore, storeToRefs } from 'pinia';
import type { TaskImages, Result, UploadResult, Task } from '@/types';
import { useDeliveryStore, useTaskStore, useSettingsStore } from '@/stores';
import { formatDateTime, showToast } from '@/utils';
import { TaskApi, OssApi } from '@/utils/api';
import { TaskUtils } from '@/utils/task';

// 声明uni全局对象，解决TypeScript错误
declare const uni: any;

export const useImagesStore = defineStore('images', () => {
	// === 核心状态 ===
	const localImages = ref<TaskImages[]>([]);
	const loading = ref(false);
	const refreshing = ref(false);

	// === 全局上传状态管理 ===
	const isUploading = ref(false); // 是否正在上传
	const isPaused = ref(false); // 是否暂停上传
	const manualPaused = ref(false); // 是否手动暂停（区分手动暂停和网络原因自动暂停）
	const uploadQueue = ref<TaskImages[]>([]); // 上传队列（全局，包含所有任务的待上传图片）
	const currentUploadIndex = ref(0); // 当前上传索引
	const currentUploadingImage = ref<TaskImages | null>(null); // 当前正在上传的图片
	const networkType = ref<string>('unknown'); // 网络类型

	// === 错误重试管理 ===
	const uploadErrorMap = ref<Map<string | number, number>>(new Map()); // 错误计数 Map<imageId, retryTimes>
	const maxRetryTimes = 3; // 最大重试次数

	// === 工具函数：按taskId查找本地图片 ===
	const getTaskLocalImages = (taskId: string | number) => {
		return localImages.value.filter((img) => img.taskId === taskId);
	};

	// === 计算属性 ===
	// 显示用的图片列表（按任务ID过滤的待显示图片）
	const taskLocalPendingmages = computed(() => (taskId: string | number) => {
		return getTaskLocalImages(taskId).filter((img) => img.imageStatus === 'pending');
	});

	// 当前所有待上传图片
	const currentImages = computed(() => {
		return localImages.value.filter((img) => img.imageStatus === 'pending');
	});

	// 任务级别的统计信息
	const getTaskStats = (taskId: string | number, photoMax: number = 0) => {
		// 获取任务信息（包含服务端数据）
		const taskStore = useTaskStore();
		const task = taskStore.tasks.find((t) => t.taskId === taskId);

		// 服务端数据
		const serverImages = task?.taskImages || []; // 服务端已确认的图片
		const serverHasTaken = serverImages.length; // 服务端已确认的拍照数量

		// 客户端本地数据
		const localImages = getTaskLocalImages(taskId);
		const pendingImages = localImages.filter((img) => img.imageStatus === 'pending'); // 待传
		const completeImages = localImages.filter((img) => img.imageStatus === 'complete'); // 已传（临时）

		const result = {
			shouldTake: photoMax, // 应拍（服务端）
			hasTaken: serverHasTaken, // 已拍（服务端已确认）
			hasUploaded: completeImages.length, // 已传（客户端临时，很快会被清空）
			pendingUpload: pendingImages.length, // 待传（客户端本地）
		};

		// 只在结果为0时输出调试信息
		if (result.shouldTake === 0 && result.hasTaken === 0 && result.pendingUpload === 0) {
			console.log(`📊 [GET_TASK_STATS] 任务 ${taskId} 统计全为0 - 找到任务: ${task ? '是' : '否'}, photoMax: ${photoMax}`);
			if (task) {
				console.log(`📊 [GET_TASK_STATS] 任务详情:`, { taskId: task.taskId, photoMax: task.photoMax, taskImages: task.taskImages?.length || 0 });
			}
		}
		return result;
	};

	// === 图片操作方法 ===
	// 添加图片
	const addImage = (image: TaskImages) => {
		localImages.value.push(image);
		saveLocalImages();

		// 发送图片添加事件（防止循环调用）
		uni.$nextTick(() => {
			uni.$emit('imageAdded', {
				taskId: image.taskId,
				imageId: image.id,
				timestamp: Date.now()
			});
		});
	};

	// 删除图片
	const removeImage = (imageId: string | number) => {
		const index = localImages.value.findIndex((img) => img.id === imageId);
		if (index > -1) {
			const removedImage = localImages.value[index];
			localImages.value.splice(index, 1);
			saveLocalImages();

			// 发送图片删除事件
			uni.$emit('imageDeleted', {
				taskId: removedImage.taskId,
				imageId: imageId,
				timestamp: Date.now()
			});
		}
	};

	// 更新图片状态
	const updateImageStatus = (imageId: string | number, status: 'pending' | 'complete') => {
		const image = localImages.value.find((img) => img.id === imageId);
		if (image) {
			const oldStatus = image.imageStatus;
			image.imageStatus = status;
			saveLocalImages();

			// 发送图片状态变化事件（防止循环调用）
			uni.$nextTick(() => {
				uni.$emit('imageStatusChanged', {
					taskId: image.taskId,
					imageId: imageId,
					oldStatus: oldStatus,
					newStatus: status,
					timestamp: Date.now()
				});
			});
		}
	};

	// 清理已上传的图片（服务器数据加载后调用）
	const cleanupUploadedImages = () => {
		localImages.value = localImages.value.filter((img) => img.imageStatus === 'pending');
		saveLocalImages();
	};

	// === 错误重试管理方法 ===
	// 增加错误计数
	const incrementErrorCount = (imageId: string | number): number => {
		const currentCount = uploadErrorMap.value.get(imageId) || 0;
		const newCount = currentCount + 1;
		uploadErrorMap.value.set(imageId, newCount);
		return newCount;
	};

	// 获取错误计数
	const getErrorCount = (imageId: string | number): number => {
		return uploadErrorMap.value.get(imageId) || 0;
	};

	// 清除错误计数
	const clearErrorCount = (imageId: string | number) => {
		uploadErrorMap.value.delete(imageId);
	};

	// 清除所有错误计数
	const clearAllErrorCounts = () => {
		uploadErrorMap.value.clear();
	};

	// 将失败的图片移到队尾
	const moveImageToQueueEnd = (imageIndex: number) => {
		if (imageIndex >= 0 && imageIndex < uploadQueue.value.length) {
			const failedImage = uploadQueue.value.splice(imageIndex, 1)[0];
			uploadQueue.value.push(failedImage);
		}
	};

	// 从队列中移除图片
	const removeImageFromQueue = (imageIndex: number) => {
		if (imageIndex >= 0 && imageIndex < uploadQueue.value.length) {
			const removedImage = uploadQueue.value.splice(imageIndex, 1)[0];
			// 清除该图片的错误计数
			if (removedImage.id) {
				clearErrorCount(removedImage.id);
			}
			return removedImage;
		}
		return null;
	};

	// 显示上传完成对话框
	const showUploadCompleteDialog = (hasErrors: boolean) => {
		if (hasErrors) {
			// 有错误的情况
			uni.showModal({
				title: '上传完成',
				content: '全部上传完成，但有部分未成功',
				showCancel: false,
				confirmText: '确定',
				confirmColor: '#ff9500', // 警告色
				success: async (res) => {
					if (res.confirm) {
						console.info('用户确认了有错误的上传完成提示');
						// 用户确认后，触发刷新
						await triggerRefreshAfterConfirm();
					}
				},
			});
		} else {
			// 没有错误的情况
			uni.showModal({
				title: '上传完成',
				content: '全部上传完成',
				showCancel: false,
				confirmText: '确定',
				confirmColor: '#007aff', // 成功色
				success: async (res) => {
					if (res.confirm) {
						console.info('用户确认了成功的上传完成提示');
						// 用户确认后，触发刷新
						await triggerRefreshAfterConfirm();
					}
				},
			});
		}
	};

	// 用户确认对话框后触发刷新
	const triggerRefreshAfterConfirm = async () => {
		console.info('用户确认后开始刷新流程');

		// 执行刷新服务器数据的逻辑
		await refreshFromServer();
	};

	// 从本地存储加载拍照数据
	const loadLocalImages = () => {
		const storedImages = uni.getStorageSync('localTaskImages');
		if (storedImages) {
			localImages.value = JSON.parse(storedImages);
		}
	};

	// 保存拍照数据到本地存储
	const saveLocalImages = () => {
		uni.setStorageSync('localTaskImages', JSON.stringify(localImages.value));
	};

	// 清除缓存数据（保留本地未上传照片）
	const clearCache = () => {
		try {
			// ❌ 不清除本地存储中的 localTaskImages，因为包含用户未上传的照片
			// uni.removeStorageSync('localTaskImages');

			// ❌ 不清除 localImages，这是用户的工作成果
			// localImages.value = [];

			// ✅ 只清除上传相关的临时状态
			uploadQueue.value = [];
			currentUploadIndex.value = 0;
			currentUploadingImage.value = null;
			uploadErrorMap.value.clear();

			// ✅ 重置上传状态
			isUploading.value = false;
			isPaused.value = false;
			manualPaused.value = false;
			loading.value = false;
			refreshing.value = false;
			networkType.value = 'unknown';

			console.info('🗑️ [IMAGES CACHE] 已清除上传状态缓存（保留本地照片）');
		} catch (error) {
			console.warn('清除图片缓存失败:', error);
		}
	};

	// 将临时图片保存为永久文件
	const saveImageToLocal = async (tempFilePath: string): Promise<string> => {
		try {
			// 尝试将临时文件保存为永久文件
			const saveFileRes = await uni.saveFile({
				tempFilePath: tempFilePath,
			});
			return saveFileRes.savedFilePath; // 返回永久文件路径
		} catch (saveError) {
			console.warn('保存永久文件失败，将使用临时文件路径:', saveError);
			// 在不支持 saveFile 的环境中，继续使用临时文件路径
			return tempFilePath;
		}
	};

	// 添加本地拍照数据 - 不再负责保存文件和创建图片对象
	const addLocalImage = async (task: any, taskImage: TaskImages) => {
		try {
			// 确保图片状态为待上传
			taskImage.imageStatus = 'pending';

			// 添加主任务图片到本地图片数组
			localImages.value.push(taskImage);

			// 处理折叠任务：为每个折叠任务创建独立的 TaskImages 并加入队列
			if (task.collapsedZoneTasks && task.collapsedZoneTasks.length > 0) {
				for (const collapsedTask of task.collapsedZoneTasks) {
					// 为每个折叠任务创建独立的图片对象
					const collapsedTaskImage: TaskImages = {
						...taskImage,
						id: TaskUtils.generateLocalId(), // 生成新的唯一ID
						taskId: collapsedTask.taskId, // 使用折叠任务的ID
						pid: taskImage.id, // 记录父图片的ID
					};
					// 将折叠任务图片也添加到本地图片数组
					localImages.value.push(collapsedTaskImage);
				}
				taskImage.pid = taskImage.id; // 给自己也设置一个pid，减少下次查询的复杂度
			}

			saveLocalImages();
			return true;
		} catch (error) {
			console.error('保存图片失败:', error);
			showToast('保存图片失败', 'none');
			return false;
		}
	};

	// 删除本地拍照数据
	const deleteLocalImage = async (imageUrl: string) => {
		const index = localImages.value.findIndex((img) => img.imageUrl === imageUrl);
		if (index !== -1) {
			try {
				// 尝试删除永久文件，如果支持的话
				try {
					await uni.removeSavedFile({
						filePath: imageUrl,
					});
				} catch (removeError) {
					console.warn('删除永久文件失败，可能不支持此操作:', removeError);
					// 在不支持 removeSavedFile 的环境中，继续执行
				}

				// 无论文件删除是否成功，都从列表中移除
				localImages.value.splice(index, 1);
				saveLocalImages();
				return true;
			} catch (error) {
				console.error('删除图片数据失败:', error);
				// 尝试从列表中移除
				try {
					localImages.value.splice(index, 1);
					saveLocalImages();
				} catch (saveError) {
					console.error('更新本地存储失败:', saveError);
				}
				return true;
			}
		}
		return false;
	};

	// 上传任务照片
	const uploadTaskImage = async (taskImages: TaskImages) => {
		loading.value = true;
		try {
			// 检查是否需要跳过上传（如果已经上传过文件）
			if (taskImages.skipUpload) {
				console.info('跳过文件上传，直接提交数据');
			} else {
				// 第一步：上传图片到OSS
				const ossData = (await OssApi.uploadToOSS(taskImages.imageUrl, {
					bucketName: 'omaps',
				})) as UploadResult;

				const fileUrl = `${ossData.bucketName}/${ossData.objectName}`;

				// 查找并更新所有相同 pid 的待上传图片（包括自己）
				if (taskImages.pid) {
					// 有pid才会有相关图片
					const relatedPendingImages = localImages.value.filter(
						(img) => img.imageStatus === 'pending' && img.pid === taskImages.pid
					);
					relatedPendingImages.forEach((img) => {
						img.imageUrl = fileUrl;
						img.skipUpload = true;
					});
					saveLocalImages();
				}
			}

			// 第二步：提交数据到服务端
			const uploadData = {
				...taskImages,
				id: null, // 提交服务端时将本地ID设置为null，让服务端生成新的ID
				shotTime: formatDateTime(taskImages.shotTime),
				createTime: formatDateTime(taskImages.createTime),
				imageStatus: 'pending',
			};

			// 上传到服务端
			const result = await TaskApi.saveTaskImage(uploadData);
			const resultTaskImages = result.data as TaskImages;

			// 更新本地图片状态为 complete
			updateImageStatus(taskImages.id!, 'complete');

			// 更新对应任务的 taskImages 数组
			const taskStore = useTaskStore();

			console.log(`🔍 [DEBUG] uploadTaskImage - 更新任务图片数组`);
			console.log(`🔍 [DEBUG] - taskId: ${taskImages.taskId}`);
			console.log(`🔍 [DEBUG] - resultTaskImages:`, resultTaskImages);

			// 使用 taskStore 的专门方法来更新 taskImages
			const updateSuccess = taskStore.updateTaskImages(taskImages.taskId, resultTaskImages);

			if (updateSuccess) {
				// 发送任务图片更新事件
				uni.$emit('taskImagesUpdated', {
					taskId: taskImages.taskId,
					timestamp: Date.now()
				});

				// 获取更新后的任务进行完成状态检查
				const updatedTask = taskStore.tasks.find((t) => t.taskId === taskImages.taskId);
				if (updatedTask) {
					checkAndUpdateTaskCompletion(updatedTask);
				}
			} else {
				console.error(`🔍 [DEBUG] - 更新任务图片失败，taskId: ${taskImages.taskId}`);
			}

			return resultTaskImages;
		} finally {
			loading.value = false;
		}
	};

	// 检查并更新任务完成状态
	const checkAndUpdateTaskCompletion = (task: any) => {
		try {
			// 获取任务的统计信息
			const stats = getTaskStats(task.taskId, task.photoMax || 0);

			// 计算总的已上传图片数量（服务端 + 本地已上传）
			const totalUploaded = task.taskImages?.length || 0;

			// 如果已上传的图片数量达到或超过要求数量，且任务状态不是已完成
			if (totalUploaded >= (task.photoMax || 0) && task.taskStatus !== 'COMPLETED') {
				console.log(`📋 [TASK_COMPLETE] 任务 ${task.taskId} 已达到完成条件，更新状态为 COMPLETED`);

				// 使用 taskStore 的方法来更新任务状态，确保响应式更新
				const taskStore = useTaskStore();
				const success = taskStore.updateTaskStatus(task.taskId, 'COMPLETED');

				if (success) {
					// 发送任务完成事件（防止循环调用）
					uni.$nextTick(() => {
						uni.$emit('taskCompleted', {
							taskId: task.taskId,
							deliveryId: task.deliveryId,
							timestamp: Date.now()
						});
					});

					console.log(`✅ [TASK_COMPLETE] 任务 ${task.taskId} 状态已更新为 COMPLETED`);
				}
			}
		} catch (error) {
			console.error('检查任务完成状态失败:', error);
		}
	};

	// 批量检查任务完成状态
	const checkBatchTaskCompletion = async () => {
		try {
			console.info('🔄 [BATCH_CHECK] 开始批量检查任务完成状态...');

			const taskStore = useTaskStore();
			let updatedCount = 0;

			// 获取所有相关的任务ID（从刚刚上传的图片中）
			const relatedTaskIds = [...new Set(uploadQueue.value.map(img => img.taskId))];

			// 检查每个相关任务的状态
			for (const taskId of relatedTaskIds) {
				const task = taskStore.tasks.find(t => t.taskId === taskId);
				if (task && task.taskStatus !== 'COMPLETED') {
					const oldStatus = task.taskStatus;
					checkAndUpdateTaskCompletion(task);

					if (task.taskStatus === 'COMPLETED' && oldStatus !== 'COMPLETED') {
						updatedCount++;
					}
				}
			}

			if (updatedCount > 0) {
				console.info(`✅ [BATCH_CHECK] 批量检查完成，更新了 ${updatedCount} 个任务状态`);

				// 发送批量更新完成事件（防止循环调用）
				uni.$nextTick(() => {
					uni.$emit('batchTaskCompleted', {
						updatedCount,
						taskIds: relatedTaskIds,
						timestamp: Date.now()
					});
				});
			} else {
				console.info('ℹ️ [BATCH_CHECK] 批量检查完成，没有任务需要更新状态');
			}
		} catch (error) {
			console.error('批量检查任务完成状态失败:', error);
		}
	};

	// 获取任务的所有本地图片
	const getLocalTaskImages = (taskId: string | number) => {
		return localImages.value.filter((img) => img.taskId === taskId);
	};

	// 检查网络状态
	const checkNetworkStatus = (): Promise<boolean> => {
		return new Promise((resolve) => {
			uni.getNetworkType({
				success: (res: any) => {
					networkType.value = res.networkType;
					// 检查是否需要在WiFi下上传
					const settingsStore = useSettingsStore();
					const wifiOnly = settingsStore.settings.wifiOnly;

					// 如果设置了仅WiFi上传，且当前不是WiFi，则返回false
					if (wifiOnly && res.networkType !== 'wifi') {
						showToast('当前设置为仅在WiFi环境下上传图片', 'none');
						resolve(false);
					} else {
						resolve(true);
					}
				},
				fail: () => {
					networkType.value = 'unknown';
					showToast('无法获取网络状态', 'none');
					resolve(false);
				},
			});
		});
	};

	// 监听网络状态变化
	const startNetworkListener = () => {
		uni.onNetworkStatusChange((res: any) => {
			const previousNetworkType = networkType.value;
			networkType.value = res.networkType;

			// 获取设置
			const settingsStore = useSettingsStore();
			const wifiOnly = settingsStore.settings.wifiOnly;

			// 如果设置了仅WiFi上传
			if (wifiOnly) {
				if (res.networkType === 'wifi') {
					// 从其他网络切换到WiFi，如果有上传任务且被自动暂停，则恢复上传
					if (previousNetworkType !== 'wifi' && isUploading.value && isPaused.value && !manualPaused.value) {
						console.info('检测到WiFi连接，自动恢复上传');
						showToast('检测到WiFi连接，自动恢复上传', 'success');
						resumeUpload();
					}
				} else if (previousNetworkType === 'wifi') {
					// 从WiFi切换到其他网络，如果有上传任务且未暂停，则自动暂停
					if (isUploading.value && !isPaused.value) {
						console.info('WiFi已断开，自动暂停上传');
						showToast('WiFi已断开，自动暂停上传', 'none');
						// 标记为自动暂停，而非手动暂停
						manualPaused.value = false;
						pauseUpload();
					}
				}
			} else {
				// 如果没有设置仅WiFi上传，则只要网络恢复且有上传任务在进行，且没有手动暂停，则继续上传
				if (res.isConnected && isUploading.value && isPaused.value && !manualPaused.value) {
					console.info('网络已恢复，自动继续上传');
					showToast('网络已恢复，自动继续上传', 'success');
					resumeUpload();
				}
			}
		});

		// 初始获取一次网络状态
		uni.getNetworkType({
			success: (res: any) => {
				networkType.value = res.networkType;
				console.info('当前网络类型:', networkType.value);
			},
		});
	};

	// 开始批量上传
	const startUpload = async () => {
		// 如果已经在上传中，则不重复开始
		if (isUploading.value) return;

		// 检查网络状态
		const canUpload = await checkNetworkStatus();
		if (!canUpload) return;

		// 获取所有待上传的图片（使用 currentImages 计算属性）
		const pendingImages = currentImages.value;
		if (pendingImages.length === 0) {
			showToast('没有待上传的图片', 'none');
			return;
		}

		// 设置上传状态
		isUploading.value = true;
		isPaused.value = false;
		uploadQueue.value = [...pendingImages]; // 创建快照
		currentUploadIndex.value = 0;
		currentUploadingImage.value = null;

		// 清除所有错误计数，重新开始
		clearAllErrorCounts();

		// 开始上传
		continueUpload();
	};

	// 继续上传
	const continueUpload = async () => {
		// 如果暂停或没有上传任务，则返回
		if (isPaused.value || !isUploading.value) return;

		// 检查网络状态
		const canUpload = await checkNetworkStatus();
		if (!canUpload) {
			// 如果不能上传，等待网络恢复
			return;
		}

		// 检查是否还有图片需要上传
		if (currentUploadIndex.value >= uploadQueue.value.length) {
			// 上传完成，检查相关任务的完成状态
			await checkBatchTaskCompletion();

			// 检查是否有错误
			const hasErrors = uploadErrorMap.value.size > 0;

			// 重置上传状态
			isUploading.value = false;
			isPaused.value = false;
			uploadQueue.value = [];
			currentUploadIndex.value = 0;
			currentUploadingImage.value = null;

			// 显示上传完成提示
			showUploadCompleteDialog(hasErrors);

			// 清除所有错误计数
			clearAllErrorCounts();

			// 注意：刷新操作将在用户确认对话框后通过事件机制触发
			return;
		}

		// 获取当前要上传的图片
		const currentImage = uploadQueue.value[currentUploadIndex.value];
		currentUploadingImage.value = currentImage;

		try {
			// 上传图片
			await uploadTaskImage(currentImage);

			// 上传成功，清除该图片的错误计数
			if (currentImage.id) {
				clearErrorCount(currentImage.id);
			}

			// 上传成功，更新原图片状态
			updateImageStatus(currentImage.id!, 'complete');

			// 触发图片上传完成事件（防止循环调用）
			uni.$nextTick(() => {
				uni.$emit('photoUploaded', {
					taskId: currentImage.taskId,
					imageId: currentImage.id,
					timestamp: Date.now()
				});
			});

			// 继续下一张
			currentUploadIndex.value++;

			// 继续上传下一张
			continueUpload();
		} catch (error) {
			console.error('上传失败:', error);

			// 增加错误计数
			const errorCount = incrementErrorCount(currentImage.id!);

			if (errorCount >= maxRetryTimes) {
				// 达到最大重试次数，从队列中移除
				console.info(`图片上传失败${maxRetryTimes}次，已跳过`, 'error');
				removeImageFromQueue(currentUploadIndex.value);

				// 注意：移除后不需要增加索引，因为后面的元素会前移
				// 继续上传下一张
				continueUpload();
			} else {
				// 未达到最大重试次数，移到队尾
				console.info(`上传失败，将重试 (${errorCount}/${maxRetryTimes})`, 'error');
				moveImageToQueueEnd(currentUploadIndex.value);

				// 注意：移到队尾后不需要增加索引，因为后面的元素会前移
				// 继续上传下一张
				continueUpload();
			}
		}
	};

	// 刷新服务器数据并清理已上传图片
	const refreshFromServer = async () => {
		try {
			console.info('准备刷新服务器数据...');

			// 清理已上传的本地图片
			cleanupUploadedImages();

			// 发送刷新消息，让相关页面监听并处理
			// 避免循环依赖，使用事件机制
			uni.$emit('uploadCompleteRefresh', {
				timestamp: Date.now(),
				source: 'imagesStore',
			});

			console.info('已发送刷新消息');
		} catch (error) {
			console.error('刷新服务器数据失败:', error);
		}
	};

	// 暂停上传
	const pauseUpload = (manual: boolean = true) => {
		if (isUploading.value) {
			isPaused.value = true;
			manualPaused.value = manual; // 记录是否是手动暂停

			if (manual) {
				showToast('已手动暂停上传', 'none');
			}
		}
	};

	// 恢复上传
	const resumeUpload = async (manual: boolean = true) => {
		if (isUploading.value && isPaused.value) {
			isPaused.value = false;

			if (manual) {
				// 如果是手动恢复，清除手动暂停标记
				manualPaused.value = false;
			}

			continueUpload();
		} else if (!isUploading.value) {
			// 如果没有上传任务，则开始新的上传
			manualPaused.value = false; // 重置手动暂停标记
			startUpload();
		}
	};

	// 获取上传状态
	const getUploadStatus = () => {
		const currentImage = uploadQueue.value[currentUploadIndex.value];
		return {
			isUploading: isUploading.value,
			isPaused: isPaused.value,
			manualPaused: manualPaused.value,
			total: uploadQueue.value.length,
			current: currentUploadIndex.value,
			networkType: networkType.value,
			isWifi: networkType.value === 'wifi',
			currentImage: currentImage || null,
		};
	};

	// 初始化时加载本地图片和启动网络监听
	loadLocalImages();
	startNetworkListener();

	return {
		// === 状态 ===
		localImages,
		loading,
		refreshing,
		isUploading,
		isPaused,
		uploadQueue,
		currentUploadingImage,

		// === 计算属性 ===
		taskLocalPendingmages,
		currentImages,

		// === 方法 ===
		getTaskLocalImages,
		getTaskStats,
		addImage,
		removeImage,
		updateImageStatus,
		cleanupUploadedImages,
		refreshFromServer,

		// === 错误重试管理方法 ===
		incrementErrorCount,
		getErrorCount,
		clearErrorCount,
		clearAllErrorCounts,
		moveImageToQueueEnd,
		removeImageFromQueue,
		showUploadCompleteDialog,
		triggerRefreshAfterConfirm,

		// === 原有方法（保持兼容性）===
		addLocalImage,
		deleteLocalImage,
		uploadTaskImage,
		getLocalTaskImages,
		loadLocalImages,
		startUpload,
		pauseUpload,
		resumeUpload,
		getUploadStatus,
		checkNetworkStatus,
		saveImageToLocal,

		// === 缓存管理 ===
		clearCache,
	};
});
