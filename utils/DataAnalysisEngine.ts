import type { Task, Delivery } from '@/types';

/**
 * 任务统计信息
 */
export interface TaskStats {
	total: number;
	spot: { total: number; completed: number; incomplete: number };
	zone: { total: number; completed: number; incomplete: number };
	byDelivery: Map<string, {
		total: number;
		completed: number;
		incomplete: number;
		spotTotal: number;
		spotCompleted: number;
		zoneTotal: number;
		zoneCompleted: number;
	}>;
	byDistrict: Map<string, { total: number; completed: number; incomplete: number }>;
	completionRate: number;
}

/**
 * 派单统计信息
 */
export interface DeliveryStats {
	total: number;
	completed: number;
	incomplete: number;
	byDate: Map<string, { total: number; completed: number; incomplete: number }>;
	byQueue: Map<string, { total: number; completed: number; incomplete: number }>;
	completionRate: number;
	criticalDeliveries: string[]; // 需要关注的派单ID
}

/**
 * 关联关系分析
 */
export interface RelationshipAnalysis {
	deliveryTaskMap: Map<string, string[]>; // 派单ID -> 任务ID列表
	taskDeliveryMap: Map<string, string>; // 任务ID -> 派单ID
	deliveryDependencies: Map<string, string[]>; // 派单依赖关系
	bottleneckTasks: string[]; // 瓶颈任务
	criticalPath: string[]; // 关键路径
}

/**
 * 缓存的计算结果
 */
export interface CachedComputations {
	// 筛选结果缓存
	filteredTasks: Map<string, Task[]>;
	filteredDeliveries: Map<string, Delivery[]>;

	// 排序结果缓存
	sortedTasks: Map<string, Task[]>;
	sortedDeliveries: Map<string, Delivery[]>;

	// 聚合统计缓存
	aggregatedStats: Map<string, any>;

	// 搜索结果缓存
	searchResults: Map<string, any[]>;
}

/**
 * 完整分析结果
 */
export interface AnalysisResult {
	taskStats: TaskStats;
	deliveryStats: DeliveryStats;
	relationships: RelationshipAnalysis;
	cachedComputations: CachedComputations;
	analysisTime: number;
	timestamp: number;
}

/**
 * 状态变化信息
 */
export interface StatusChange {
	type: 'TASK_STATUS' | 'IMAGE_UPLOAD' | 'COMPLETION' | 'TASK_UPDATE';
	taskId: string;
	deliveryId: string;
	oldValue: any;
	newValue: any;
	affectedAreas: string[];
	timestamp: number;
}

/**
 * 数据分析引擎
 * 负责对任务和派单数据进行深度分析和预处理
 */
export class DataAnalysisEngine {
	private analysisResult: AnalysisResult | null = null;
	private readonly CACHE_KEY = 'analysis_result_cache';

	/**
	 * 执行全量数据分析
	 */
	async performFullAnalysis(tasks: Task[], deliveries: Delivery[]): Promise<AnalysisResult> {
		console.info('🔍 [ANALYSIS] 开始全量数据分析');
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		try {
			// 1. 基础统计分析
			const taskStats = this.analyzeTaskStats(tasks);
			const deliveryStats = this.analyzeDeliveryStats(deliveries, tasks);

			// 2. 关联关系分析
			const relationships = this.analyzeRelationships(tasks, deliveries);

			// 3. 预计算常用查询结果
			const cachedComputations = this.precomputeQueries(tasks, deliveries);

			const analysisTime = (typeof performance !== 'undefined' ? performance.now() : Date.now()) - startTime;

			this.analysisResult = {
				taskStats,
				deliveryStats,
				relationships,
				cachedComputations,
				analysisTime,
				timestamp: Date.now()
			};

			// 保存分析结果到缓存
			this.saveAnalysisToCache();

			console.info(`🔍 [ANALYSIS] 全量分析完成，耗时: ${analysisTime.toFixed(2)}ms`);
			console.info(`📊 [ANALYSIS] 任务总数: ${taskStats.total}, 派单总数: ${deliveryStats.total}`);
			console.info(`📈 [ANALYSIS] 任务完成率: ${(taskStats.completionRate * 100).toFixed(1)}%, 派单完成率: ${(deliveryStats.completionRate * 100).toFixed(1)}%`);

			return this.analysisResult;
		} catch (error) {
			console.error('❌ [ANALYSIS] 全量分析失败:', error);
			throw error;
		}
	}

	/**
	 * 增量更新分析结果
	 */
	updateAnalysis(changes: StatusChange[]): void {
		if (!this.analysisResult) {
			console.warn('⚠️ [ANALYSIS] 没有基础分析结果，无法进行增量更新');
			return;
		}

		console.info(`🔄 [ANALYSIS] 开始增量更新分析，变化数量: ${changes.length}`);
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		try {
			// 按变化类型分组处理
			const changesByType = this.groupChangesByType(changes);

			// 更新任务统计
			if (changesByType.has('TASK_STATUS') || changesByType.has('COMPLETION')) {
				this.updateTaskStats(changesByType);
			}

			// 更新派单统计
			if (changesByType.has('TASK_STATUS') || changesByType.has('COMPLETION')) {
				this.updateDeliveryStats(changesByType);
			}

			// 更新缓存的计算结果
			this.updateCachedComputations(changes);

			// 更新时间戳
			this.analysisResult.timestamp = Date.now();

			// 保存更新后的分析结果
			this.saveAnalysisToCache();

			const updateTime = (typeof performance !== 'undefined' ? performance.now() : Date.now()) - startTime;
			console.info(`🔄 [ANALYSIS] 增量更新完成，耗时: ${updateTime.toFixed(2)}ms`);
		} catch (error) {
			console.error('❌ [ANALYSIS] 增量更新失败:', error);
		}
	}

	/**
	 * 分析任务统计
	 */
	private analyzeTaskStats(tasks: Task[]): TaskStats {
		const stats: TaskStats = {
			total: tasks.length,
			spot: { total: 0, completed: 0, incomplete: 0 },
			zone: { total: 0, completed: 0, incomplete: 0 },
			byDelivery: new Map(),
			byDistrict: new Map(),
			completionRate: 0
		};

		let totalCompleted = 0;

		tasks.forEach(task => {
			const isSpot = task.spotId !== null;
			const isCompleted = task.taskStatus === 'COMPLETED';

			// 按类型统计
			if (isSpot) {
				stats.spot.total++;
				if (isCompleted) {
					stats.spot.completed++;
					totalCompleted++;
				} else {
					stats.spot.incomplete++;
				}
			} else {
				stats.zone.total++;
				if (isCompleted) {
					stats.zone.completed++;
					totalCompleted++;
				} else {
					stats.zone.incomplete++;
				}
			}

			// 按派单统计
			const deliveryId = task.deliveryId;
			if (!stats.byDelivery.has(deliveryId)) {
				stats.byDelivery.set(deliveryId, {
					total: 0,
					completed: 0,
					incomplete: 0,
					spotTotal: 0,
					spotCompleted: 0,
					zoneTotal: 0,
					zoneCompleted: 0
				});
			}

			const deliveryStats = stats.byDelivery.get(deliveryId)!;
			deliveryStats.total++;

			if (isSpot) {
				deliveryStats.spotTotal++;
				if (isCompleted) deliveryStats.spotCompleted++;
			} else {
				deliveryStats.zoneTotal++;
				if (isCompleted) deliveryStats.zoneCompleted++;
			}

			if (isCompleted) {
				deliveryStats.completed++;
			} else {
				deliveryStats.incomplete++;
			}

			// 按区域统计
			const district = task.belongDistrict || 'unknown';
			if (!stats.byDistrict.has(district)) {
				stats.byDistrict.set(district, { total: 0, completed: 0, incomplete: 0 });
			}

			const districtStats = stats.byDistrict.get(district)!;
			districtStats.total++;
			if (isCompleted) {
				districtStats.completed++;
			} else {
				districtStats.incomplete++;
			}
		});

		stats.completionRate = stats.total > 0 ? totalCompleted / stats.total : 0;

		return stats;
	}

	/**
	 * 分析派单统计
	 */
	private analyzeDeliveryStats(deliveries: Delivery[], tasks: Task[]): DeliveryStats {
		const stats: DeliveryStats = {
			total: deliveries.length,
			completed: 0,
			incomplete: 0,
			byDate: new Map(),
			byQueue: new Map(),
			completionRate: 0,
			criticalDeliveries: []
		};

		// 创建派单任务映射
		const deliveryTaskMap = new Map<string, Task[]>();
		tasks.forEach(task => {
			const deliveryId = task.deliveryId;
			if (!deliveryTaskMap.has(deliveryId)) {
				deliveryTaskMap.set(deliveryId, []);
			}
			deliveryTaskMap.get(deliveryId)!.push(task);
		});

		deliveries.forEach(delivery => {
			const deliveryTasks = deliveryTaskMap.get(delivery.deliveryId) || [];
			const completedTasks = deliveryTasks.filter(t => t.taskStatus === 'COMPLETED').length;
			const isCompleted = deliveryTasks.length > 0 && completedTasks === deliveryTasks.length;

			if (isCompleted) {
				stats.completed++;
			} else {
				stats.incomplete++;

				// 标记关键派单（完成率低于50%的）
				const completionRate = deliveryTasks.length > 0 ? completedTasks / deliveryTasks.length : 0;
				if (completionRate < 0.5) {
					stats.criticalDeliveries.push(delivery.deliveryId);
				}
			}

			// 按日期统计
			const dateStr = delivery.deliveryDate ? new Date(delivery.deliveryDate).toISOString().split('T')[0] : 'unknown';
			if (!stats.byDate.has(dateStr)) {
				stats.byDate.set(dateStr, { total: 0, completed: 0, incomplete: 0 });
			}
			const dateStats = stats.byDate.get(dateStr)!;
			dateStats.total++;
			if (isCompleted) {
				dateStats.completed++;
			} else {
				dateStats.incomplete++;
			}

			// 按队列统计
			const queueId = delivery.queueId || 'unknown';
			if (!stats.byQueue.has(queueId)) {
				stats.byQueue.set(queueId, { total: 0, completed: 0, incomplete: 0 });
			}
			const queueStats = stats.byQueue.get(queueId)!;
			queueStats.total++;
			if (isCompleted) {
				queueStats.completed++;
			} else {
				queueStats.incomplete++;
			}
		});

		stats.completionRate = stats.total > 0 ? stats.completed / stats.total : 0;

		return stats;
	}

	/**
	 * 分析关联关系
	 */
	private analyzeRelationships(tasks: Task[], deliveries: Delivery[]): RelationshipAnalysis {
		const relationships: RelationshipAnalysis = {
			deliveryTaskMap: new Map(),
			taskDeliveryMap: new Map(),
			deliveryDependencies: new Map(),
			bottleneckTasks: [],
			criticalPath: []
		};

		// 构建派单-任务映射
		tasks.forEach(task => {
			const deliveryId = task.deliveryId;

			// 派单 -> 任务列表
			if (!relationships.deliveryTaskMap.has(deliveryId)) {
				relationships.deliveryTaskMap.set(deliveryId, []);
			}
			relationships.deliveryTaskMap.get(deliveryId)!.push(task.taskId);

			// 任务 -> 派单
			relationships.taskDeliveryMap.set(task.taskId, deliveryId);
		});

		// 分析瓶颈任务（图片数量多且未完成的任务）
		relationships.bottleneckTasks = tasks
			.filter(task => task.taskStatus !== 'COMPLETED' && (task.photoMax || 0) > 5)
			.sort((a, b) => (b.photoMax || 0) - (a.photoMax || 0))
			.slice(0, 10)
			.map(task => task.taskId);

		return relationships;
	}

	/**
	 * 预计算常用查询
	 */
	private precomputeQueries(tasks: Task[], deliveries: Delivery[]): CachedComputations {
		const cached: CachedComputations = {
			filteredTasks: new Map(),
			filteredDeliveries: new Map(),
			sortedTasks: new Map(),
			sortedDeliveries: new Map(),
			aggregatedStats: new Map(),
			searchResults: new Map()
		};

		// 预计算常用筛选结果
		cached.filteredTasks.set('completed', tasks.filter(t => t.taskStatus === 'COMPLETED'));
		cached.filteredTasks.set('incomplete', tasks.filter(t => t.taskStatus !== 'COMPLETED'));
		cached.filteredTasks.set('spot', tasks.filter(t => t.spotId !== null));
		cached.filteredTasks.set('zone', tasks.filter(t => t.spotId === null));

		// 预计算排序结果
		cached.sortedTasks.set('by_date', [...tasks].sort((a, b) =>
			new Date(a.createTime || 0).getTime() - new Date(b.createTime || 0).getTime()
		));
		cached.sortedDeliveries.set('by_date', [...deliveries].sort((a, b) =>
			new Date(a.deliveryDate || 0).getTime() - new Date(b.deliveryDate || 0).getTime()
		));

		return cached;
	}

	/**
	 * 按类型分组变化
	 */
	private groupChangesByType(changes: StatusChange[]): Map<string, StatusChange[]> {
		const grouped = new Map<string, StatusChange[]>();

		changes.forEach(change => {
			if (!grouped.has(change.type)) {
				grouped.set(change.type, []);
			}
			grouped.get(change.type)!.push(change);
		});

		return grouped;
	}

	/**
	 * 更新任务统计
	 */
	private updateTaskStats(changesByType: Map<string, StatusChange[]>): void {
		// 实现增量更新逻辑
		console.info('🔄 [ANALYSIS] 更新任务统计');
	}

	/**
	 * 更新派单统计
	 */
	private updateDeliveryStats(changesByType: Map<string, StatusChange[]>): void {
		// 实现增量更新逻辑
		console.info('🔄 [ANALYSIS] 更新派单统计');
	}

	/**
	 * 更新缓存的计算结果
	 */
	private updateCachedComputations(changes: StatusChange[]): void {
		if (!this.analysisResult) return;

		// 清除受影响的缓存
		changes.forEach(change => {
			change.affectedAreas.forEach(area => {
				this.analysisResult!.cachedComputations.filteredTasks.delete(area);
				this.analysisResult!.cachedComputations.searchResults.delete(area);
			});
		});
	}

	/**
	 * 获取分析结果
	 */
	getAnalysisResult(): AnalysisResult | null {
		return this.analysisResult;
	}

	/**
	 * 保存分析结果到缓存
	 */
	private saveAnalysisToCache(): void {
		try {
			if (this.analysisResult) {
				// 将 Map 转换为可序列化的对象
				const serializable = this.convertMapsToObjects(this.analysisResult);
				uni.setStorageSync(this.CACHE_KEY, JSON.stringify(serializable));
			}
		} catch (error) {
			console.warn('⚠️ [ANALYSIS] 保存分析结果到缓存失败:', error);
		}
	}

	/**
	 * 从缓存加载分析结果
	 */
	loadAnalysisFromCache(): AnalysisResult | null {
		try {
			const cached = uni.getStorageSync(this.CACHE_KEY);
			if (cached) {
				const parsed = JSON.parse(cached);
				this.analysisResult = this.convertObjectsToMaps(parsed);
				return this.analysisResult;
			}
		} catch (error) {
			console.warn('⚠️ [ANALYSIS] 从缓存加载分析结果失败:', error);
		}
		return null;
	}

	/**
	 * 将 Map 转换为可序列化的对象
	 */
	private convertMapsToObjects(result: AnalysisResult): any {
		// 实现 Map 到 Object 的转换
		return {
			...result,
			taskStats: {
				...result.taskStats,
				byDelivery: Object.fromEntries(result.taskStats.byDelivery),
				byDistrict: Object.fromEntries(result.taskStats.byDistrict)
			},
			deliveryStats: {
				...result.deliveryStats,
				byDate: Object.fromEntries(result.deliveryStats.byDate),
				byQueue: Object.fromEntries(result.deliveryStats.byQueue)
			},
			relationships: {
				...result.relationships,
				deliveryTaskMap: Object.fromEntries(result.relationships.deliveryTaskMap),
				taskDeliveryMap: Object.fromEntries(result.relationships.taskDeliveryMap),
				deliveryDependencies: Object.fromEntries(result.relationships.deliveryDependencies)
			}
		};
	}

	/**
	 * 将对象转换为 Map
	 */
	private convertObjectsToMaps(obj: any): AnalysisResult {
		// 实现 Object 到 Map 的转换
		return {
			...obj,
			taskStats: {
				...obj.taskStats,
				byDelivery: new Map(Object.entries(obj.taskStats.byDelivery)),
				byDistrict: new Map(Object.entries(obj.taskStats.byDistrict))
			},
			deliveryStats: {
				...obj.deliveryStats,
				byDate: new Map(Object.entries(obj.deliveryStats.byDate)),
				byQueue: new Map(Object.entries(obj.deliveryStats.byQueue))
			},
			relationships: {
				...obj.relationships,
				deliveryTaskMap: new Map(Object.entries(obj.relationships.deliveryTaskMap)),
				taskDeliveryMap: new Map(Object.entries(obj.relationships.taskDeliveryMap)),
				deliveryDependencies: new Map(Object.entries(obj.relationships.deliveryDependencies))
			},
			cachedComputations: {
				filteredTasks: new Map(),
				filteredDeliveries: new Map(),
				sortedTasks: new Map(),
				sortedDeliveries: new Map(),
				aggregatedStats: new Map(),
				searchResults: new Map()
			}
		};
	}

	/**
	 * 清除分析缓存
	 */
	clearAnalysisCache(): void {
		try {
			uni.removeStorageSync(this.CACHE_KEY);
			this.analysisResult = null;
			console.info('🗑️ [ANALYSIS] 已清除分析缓存');
		} catch (error) {
			console.warn('⚠️ [ANALYSIS] 清除分析缓存失败:', error);
		}
	}
}

// 创建全局实例
export const dataAnalysisEngine = new DataAnalysisEngine();
