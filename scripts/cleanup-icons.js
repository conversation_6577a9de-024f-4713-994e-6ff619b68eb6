#!/usr/bin/env node

/**
 * 清理未使用的图标脚本
 * 
 * 功能：
 * 1. 扫描Vue文件中实际使用的图标
 * 2. 清除CSS中未使用的图标定义
 * 3. 生成新的精简版icons.css
 */

const fs = require('fs');
const path = require('path');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');
const CSS_FILE = path.join(ROOT_DIR, 'static', 'css', 'icons.css');

// Vue文件列表
const VUE_FILES = [
  'pages/delivery/index.vue',
  'pages/delivery/components/delivery-navbar.vue',
  'pages/images/components/images-navbar.vue',
  'pages/my/index.vue',
  'components/custom-tabbar.vue',
  'pages/task/index.vue',
  'pages/task/task-detail.vue',
  'pages/task/components/task-navbar.vue'
];

console.log('🧹 开始清理未使用的图标...');

// 扫描Vue文件中使用的图标
function scanUsedIcons() {
  const usedIcons = new Set();
  
  VUE_FILES.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 查找 icon-xxx 类名
      const iconRegex = /icon-([a-zA-Z-]+)/g;
      let match;
      
      while ((match = iconRegex.exec(content)) !== null) {
        const iconName = match[1];
        usedIcons.add(iconName);
      }
      
      console.log(`📄 扫描 ${filePath}: 找到 ${[...content.matchAll(/icon-([a-zA-Z-]+)/g)].length} 个图标引用`);
    }
  });
  
  return usedIcons;
}

// 读取当前CSS中的图标定义
function readCurrentIcons() {
  const content = fs.readFileSync(CSS_FILE, 'utf8');
  const iconRegex = /\.icon-([^:]+)::before\s*{\s*content:\s*"\\([^"]+)";?\s*}[^}]*(?:\/\*[^*]*\*\/)?/g;
  const icons = {};
  
  let match;
  while ((match = iconRegex.exec(content)) !== null) {
    const iconName = match[1];
    const unicode = match[2];
    const fullMatch = match[0];
    icons[iconName] = { unicode, definition: fullMatch };
  }
  
  return icons;
}

// 生成清理后的CSS
function generateCleanCSS() {
  const usedIcons = scanUsedIcons();
  const currentIcons = readCurrentIcons();
  
  console.log(`\n📊 统计结果:`);
  console.log(`已使用图标: ${usedIcons.size}`);
  console.log(`已定义图标: ${Object.keys(currentIcons).length}`);
  
  // 找出未使用的图标
  const unusedIcons = Object.keys(currentIcons).filter(icon => !usedIcons.has(icon));
  console.log(`未使用图标: ${unusedIcons.length}`);
  
  if (unusedIcons.length > 0) {
    console.log('\n🗑️  将要删除的图标:');
    unusedIcons.forEach(icon => {
      console.log(`   icon-${icon}`);
    });
  }
  
  // 读取原始CSS文件
  let cssContent = fs.readFileSync(CSS_FILE, 'utf8');
  
  // 删除未使用的图标定义
  unusedIcons.forEach(iconName => {
    const iconDef = currentIcons[iconName];
    if (iconDef) {
      // 创建更精确的正则表达式来匹配图标定义
      const escapedDef = iconDef.definition.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(escapedDef, 'g');
      cssContent = cssContent.replace(regex, '');
      
      // 也删除可能的注释行
      const commentRegex = new RegExp(`\\/\\*[^*]*${iconName}[^*]*\\*\\/`, 'g');
      cssContent = cssContent.replace(commentRegex, '');
    }
  });
  
  // 清理多余的空行
  cssContent = cssContent.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  return cssContent;
}

// 备份原文件
function backupOriginalFile() {
  const backupPath = CSS_FILE + '.backup.' + Date.now();
  fs.copyFileSync(CSS_FILE, backupPath);
  console.log(`💾 原文件已备份到: ${backupPath}`);
  return backupPath;
}

// 主函数
function main() {
  try {
    // 备份原文件
    const backupPath = backupOriginalFile();
    
    // 生成清理后的CSS
    const cleanCSS = generateCleanCSS();
    
    // 写入新文件
    fs.writeFileSync(CSS_FILE, cleanCSS);
    
    console.log('\n✅ 图标清理完成!');
    console.log(`📁 清理后的文件: ${CSS_FILE}`);
    console.log(`📁 备份文件: ${backupPath}`);
    
    // 验证清理结果
    console.log('\n🔍 验证清理结果...');
    const usedIcons = scanUsedIcons();
    const newIcons = readCurrentIcons();
    
    console.log(`清理后已定义图标: ${Object.keys(newIcons).length}`);
    console.log(`实际使用图标: ${usedIcons.size}`);
    
    // 检查是否有遗漏
    const missingIcons = [...usedIcons].filter(icon => !newIcons[icon]);
    if (missingIcons.length > 0) {
      console.log('⚠️  警告: 以下使用的图标没有定义:');
      missingIcons.forEach(icon => {
        console.log(`   icon-${icon}`);
      });
    } else {
      console.log('✅ 所有使用的图标都有定义');
    }
    
  } catch (error) {
    console.error('❌ 清理过程中出错:', error);
  }
}

// 运行脚本
main();
