/** OMaps —— TaskImages, 投放订单点位上传的图片 */
export interface TaskImages {
	/** 主键 */
	id?: number | string;
	/** 关联上刊任务 */
	taskId?: number | string;
	/** 图片状态 */
	imageStatus?: string;
	/** 经度 */
	imageLongitude?: string;
	/** 纬度 */
	imageLatitude?: string;
	/** 名称 */
	imageName?: string;
	/** 上传者 */
	uploadUser?: string;
	/** 文件名称 */
	fileName?: string;
	/** 文件大小 */
	fileSize?: number;
	/** 上传时间 */
	uploadTime?: Date;
	/** 图片地址 */
	imageUrl?: string;
	/** 图片说明 */
	comments?: string;
	/** 关联小区 */
	zoneId?: number | string;
	/** 关联点位 */
	spotId?: number | string | undefined;
	/** 点位编码 */
	spotCode?: string | undefined;
	/** 拍摄时间 */
	shotTime?: Date;
	/** 拍摄位置 */
	shotLocation?: string;
	/** 点位故障描述 */
	spotFailReason?: string;
	/** 数据状态 */
	status?: number;
	/** True 为不能删，False为可以删除, 是否为保留数据 */
	reserved?: number;
	/** 版本号 */
	reversion?: number;
	/** 备注 */
	description?: string;
	/** 数据创建时间 */
	createTime?: Date;
	/** 数据更新时间 */
	updateTime?: Date;
	/** 创建人 */
	createBy?: string;
	/** 最后修改 */
	updateBy?: string;
	/** 排序值 */
	ranking?: number;

	// local only
	/** 父图片ID（用于折叠任务） */
	pid?: string;
	/** 是否跳过上传 */
	skipUpload?: boolean;
}
