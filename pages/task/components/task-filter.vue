<template>
  <view class="filter-tabs" :style="dynamicFontStyle" @click.stop>
    <!-- 主筛选选项卡 -->
    <view class="main-tabs">
      <view
        class="tab-item"
        v-for="(tab, index) in tabs"
        :key="index"
        :class="{ active: activeTabIndex === index }"
        @click.stop="selectTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <text class="tab-icon">{{ activeTabIndex === index && showDropdown ? '▲' : '▼' }}</text>
      </view>
    </view>

    <!-- 下拉菜单 -->
    <view class="dropdown-menu" v-if="showDropdown">
      <!-- 区域选项 -->
      <view
        v-if="activeTabIndex === 0"
        class="dropdown-item"
        v-for="(item, index) in dropdownItems"
        :key="index"
        :class="{ active: tabs[activeTabIndex].name === item.name }"
        :data-type="item.type"
        @click.stop="selectDropdownItem(item)"
      >
        <text v-if="item.value === 'all'" class="dropdown-text">{{ item.name }}</text>
        <view v-else class="district-info">
          <text class="district-name">{{ item.name }}</text>
          <view class="district-stats">
            <text class="stat-item total">总: {{ item.totalCount || 0 }}</text>
            <text class="stat-item completed">完: {{ item.completedCount || 0 }}</text>
            <text class="stat-item incomplete" :class="{ 'zero-count': (item.incompleteCount || 0) === 0 }">未: {{ item.incompleteCount || 0 }}</text>
            <text class="stat-item pending" :class="{ 'zero-count': (item.pendingCount || 0) === 0 }">待: {{ item.pendingCount || 0 }}</text>
          </view>
        </view>
      </view>

      <!-- 小区选项 -->
      <view
        v-if="activeTabIndex === 1"
        class="dropdown-item"
        v-for="(item, index) in dropdownItems"
        :key="index"
        :class="{ active: tabs[activeTabIndex].name === item.name }"
        :data-type="item.type"
        @click.stop="selectDropdownItem(item)"
      >
        <text v-if="item.value === 'all'" class="dropdown-text">{{ item.name }}</text>
        <view v-else class="zone-info">
          <text class="zone-name">{{ item.name }} ({{ item.district }})</text>
          <text class="zone-address">{{ item.address || '地址未设置' }}</text>
          <view class="zone-stats">
            <text class="stat-item total">总: {{ item.totalCount || 0 }}</text>
            <text class="stat-item completed">完: {{ item.completedCount || 0 }}</text>
            <text class="stat-item incomplete" :class="{ 'zero-count': (item.incompleteCount || 0) === 0 }">未: {{ item.incompleteCount || 0 }}</text>
            <text class="stat-item pending" :class="{ 'zero-count': (item.pendingCount || 0) === 0 }">待: {{ item.pendingCount || 0 }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 定义组件属性
const props = defineProps({
  // 区域相关
  districtNames: {
    type: Array,
    default: () => []
  },
  selectedDistrictName: {
    type: String,
    default: ''
  },
  selectedDistrictIndex: {
    type: Number,
    default: 0
  },

  // 小区相关
  zoneNames: {
    type: Array,
    default: () => []
  },
  selectedZoneName: {
    type: String,
    default: ''
  },
  selectedZoneIndex: {
    type: Number,
    default: 0
  },

  // 小区详细信息
  zoneDetails: {
    type: Array,
    default: () => []
  },

  // 区域详细信息
  districtDetails: {
    type: Array,
    default: () => []
  }
});

// 定义筛选选项卡
const tabs = ref([
  { name: '选择区域', value: 'district' },
  { name: '选择小区', value: 'zone' }
]);

// 当前激活的选项卡索引
const activeTabIndex = ref(0);

// 是否显示下拉菜单
const showDropdown = ref(false);

// 初始化选项卡值
onMounted(() => {
  // 如果已经选择了区域，更新区域选项卡的名称
  if (props.selectedDistrictName) {
    tabs.value[0].name = props.selectedDistrictName;
  }

  // 如果已经选择了小区，更新小区选项卡的名称
  if (props.selectedZoneName) {
    tabs.value[1].name = props.selectedZoneName;
  }
});

// 定义下拉菜单项类型
interface DropdownItem {
  name: string;
  value: string;
  type?: string;
  index?: number;
  // 小区详细信息
  address?: string;
  totalCount?: number;
  completedCount?: number;
  incompleteCount?: number;
  pendingCount?: number;
  district?: string;
}

// 下拉菜单选项
const dropdownItems = computed<DropdownItem[]>(() => {
  switch (activeTabIndex.value) {
    case 0: // 选择区域
      // 将区域数据转换为下拉菜单项
      const districtItems: DropdownItem[] = [];

      // 添加"显示全部"选项
      districtItems.push({ name: '显示全部', value: 'all' });

      // 添加区域选项
      if (props.districtNames && props.districtNames.length > 0) {
        props.districtNames.forEach((district: string, index: number) => {
          // 查找区域详细信息
          const districtDetail = props.districtDetails && props.districtDetails.length > 0
            ? props.districtDetails.find((detail: any) => detail.name === district)
            : null;

          districtItems.push({
            name: district,
            value: `district_${index}`,
            type: 'district',
            index: index,
            // 添加区域详细信息
            totalCount: districtDetail?.totalTasks || 0,
            completedCount: districtDetail?.completedTasks || 0,
            incompleteCount: districtDetail?.incompleteTasks || 0,
            pendingCount: districtDetail?.pendingUploadTasks || 0
          });
        });
      }

      return districtItems;

    case 1: // 选择小区
      // 将小区数据转换为下拉菜单项
      const zoneItems: DropdownItem[] = [];

      // 添加"显示全部"选项
      zoneItems.push({ name: '显示全部', value: 'all' });

      // 添加小区选项
      if (props.zoneNames && props.zoneNames.length > 0) {
        props.zoneNames.forEach((zone: string, index: number) => {
          // 查找小区详细信息
          const zoneDetail = props.zoneDetails && props.zoneDetails.length > 0
            ? props.zoneDetails.find((detail: any) => detail.zoneName === zone)
            : null;

          // 如果选择了区域，则只显示该区域下的小区
          if (props.selectedDistrictName && zoneDetail && zoneDetail.belongDistrict !== props.selectedDistrictName) {
            return; // 跳过不属于所选区域的小区
          }

          zoneItems.push({
            name: zone,
            value: `zone_${index}`,
            type: 'zone',
            index: index,
            // 添加小区详细信息
            address: zoneDetail?.zoneAddress || '',
            district: zoneDetail?.belongDistrict || '',
            totalCount: zoneDetail?.totalTasks || 0,
            completedCount: zoneDetail?.completedTasks || 0,
            incompleteCount: zoneDetail?.incompleteTasks || 0,
            pendingCount: zoneDetail?.pendingUploadTasks || 0
          });
        });
      }

      return zoneItems;

    default:
      return [];
  }
});

// 定义事件
const emit = defineEmits(['filter-change', 'district-change', 'zone-change', 'clear-district', 'clear-zone']);

// 从本地存储加载下拉菜单状态
const loadDropdownState = () => {
  try {
    const activeTabIndexValue = uni.getStorageSync('task_filter_active_tab_index');
    return {
      showDropdown: false, // 页面初始化时总是关闭下拉菜单
      activeTabIndex: activeTabIndexValue ? parseInt(activeTabIndexValue) : 0
    };
  } catch (error) {
    console.warn('加载下拉菜单状态失败:', error);
    return { showDropdown: false, activeTabIndex: 0 };
  }
};

// 保存下拉菜单状态到本地存储
const saveDropdownState = () => {
  try {
    // 只保存激活的选项卡索引，不保存下拉菜单显示状态
    uni.setStorageSync('task_filter_active_tab_index', activeTabIndex.value.toString());
  } catch (error) {
    console.warn('保存下拉菜单状态失败:', error);
  }
};

// 加载下拉菜单状态
const dropdownState = loadDropdownState();
showDropdown.value = dropdownState.showDropdown;
activeTabIndex.value = dropdownState.activeTabIndex;

// 选择选项卡
const selectTab = (index: number) => {
  if (activeTabIndex.value === index) {
    // 如果点击的是当前激活的选项卡，则切换下拉菜单的显示状态
    showDropdown.value = !showDropdown.value;
  } else {
    // 如果点击的是其他选项卡，则切换到该选项卡并显示下拉菜单
    activeTabIndex.value = index;
    showDropdown.value = true;
  }
  // 保存下拉菜单状态
  saveDropdownState();
};

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
  '--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
  try {
    const savedFontScale = uni.getStorageSync('fontScale') || 1;
    currentFontScale.value = savedFontScale;
    console.info(`📄 [TASK-FILTER] 组件字体大小初始化: 缩放 ${savedFontScale}`);
  } catch (error) {
    console.error('组件字体大小初始化失败:', error);
  }
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
  const { size, scale } = data;
  currentFontScale.value = scale;
  console.info(`📄 [TASK-FILTER] 组件字体大小已更新: ${size} (缩放: ${scale})`);
};

// 点击外部关闭下拉菜单
onMounted(() => {
  // 确保页面初始化时下拉菜单是关闭的
  showDropdown.value = false;

  // 初始化字体大小
  initFontSize();

  // 监听字体大小变化
  uni.$on('fontSizeChanged', handleFontSizeChange);

  // 仅在H5环境中添加document事件监听器
  // #ifdef H5
  if (typeof document !== 'undefined') {
    document.addEventListener('click', (event) => {
      // 如果点击的不是筛选选项卡内的元素，则关闭下拉菜单
      const target = event.target as HTMLElement;
      if (!target.closest('.filter-tabs')) {
        if (showDropdown.value) {
          showDropdown.value = false;
          // 不需要保存下拉菜单状态，因为我们不再持久化显示状态
        }
      }
    });
  }
  // #endif

  // 在非H5环境中，可以使用其他方式处理点击外部关闭下拉菜单
  // 例如，可以在父组件中处理页面点击事件
});

onUnmounted(() => {
  // 清理事件监听
  uni.$off('fontSizeChanged', handleFontSizeChange);
});

// 选择下拉菜单项
const selectDropdownItem = (item: DropdownItem) => {
  switch (activeTabIndex.value) {
    case 0: // 选择区域
      if (item.value === 'all') {
        // 清除区域筛选
        emit('clear-district');
        tabs.value[activeTabIndex.value].name = '选择区域';
      } else if (item.type === 'district' && item.index !== undefined) {
        // 选择区域
        const event = { detail: { value: item.index } };
        emit('district-change', event);
        tabs.value[activeTabIndex.value].name = item.name;
      }
      break;

    case 1: // 选择小区
      if (item.value === 'all') {
        // 清除小区筛选
        emit('clear-zone');
        tabs.value[activeTabIndex.value].name = '选择小区';
      } else if (item.type === 'zone' && item.index !== undefined) {
        // 选择小区
        const event = { detail: { value: item.index } };
        emit('zone-change', event);
        tabs.value[activeTabIndex.value].name = item.name;
      }
      break;
  }

  // 隐藏下拉菜单
  showDropdown.value = false;
  // 不需要保存下拉菜单状态，因为我们不再持久化显示状态
};
</script>

<style lang="scss" scoped>
.filter-tabs {
  position: relative;
  width: 100%;

  // 应用字体缩放到文本元素
  .tab-text, .dropdown-text, .district-name, .zone-name, .zone-address, .stat-item {
    font-size: calc(1em * var(--font-scale, 1)) !important;
    line-height: calc(1.4 * var(--font-scale, 1)) !important;
  }

  .main-tabs {
    display: flex;
    width: 100%;
    height: 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #EEEEEE;

    .tab-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;

      &.active {
        color: #0CBFB0;
      }

      .tab-text {
        font-size: 16px;
        margin-right: 5px;
        white-space: normal; /* 允许换行 */
        word-wrap: break-word; /* 长文本换行 */
        text-align: center; /* 居中对齐 */
        max-width: 80%;
        line-height: 1.3; /* 紧凑行高 */
      }

      .tab-icon {
        font-size: 12px;
      }
    }
  }

  .dropdown-menu {
    position: absolute;
    width: 100%;
    max-height: 80vh; /* 限制最大高度为视口高度的80% */
    overflow-y: auto; /* 启用垂直滚动 */
    background-color: #FFFFFF;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;
    border-top: 1px solid #EEEEEE;

    /* 优化滚动条样式 */
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */

    /* 自定义滚动条样式（仅在支持的浏览器中生效） */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    .dropdown-item {
      min-height: 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 10px 20px;
      border-bottom: 1px solid #F5F5F5;

      &:active {
        background-color: #F5F5F5;
      }

      &.active {
        color: #0CBFB0;
        font-weight: bold;
        position: relative;

        &::after {
          content: "✓";
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          color: #0CBFB0;
          font-size: 18px;
        }
      }

      .dropdown-text {
        font-size: 16px;
      }

      // 区域项样式
      &[data-type="district"] {
        padding: 15px 20px;

        .district-info {
          display: flex;
          flex-direction: column;

          .district-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
          }

          .district-stats {
            display: flex;
            font-size: 16px;

            .stat-item {
              margin-right: 15px;

              &.total {
                color: #409EFF;
              }

              &.completed {
                color: #67C23A;
              }

              &.pending {
                color: #dd524d;

                &.zero-count {
                  color: #999999;
                }
              }

              &.incomplete {
                color: #F56C6C;

                &.zero-count {
                  color: #999999;
                }
              }
            }
          }
        }
      }

      // 小区项样式
      &[data-type="zone"] {
        padding: 15px 20px;

        .zone-info {
          display: flex;
          flex-direction: column;

          .zone-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
          }

          .zone-address {
            font-size: 15px;
            color: #999;
            margin-bottom: 8px;
            white-space: normal; /* 允许换行 */
            word-wrap: break-word; /* 长文本换行 */
            word-break: break-all; /* 必要时断词 */
            line-height: 1.4; /* 合适的行高 */
          }

          .zone-stats {
            display: flex;
            font-size: 16px;

            .stat-item {
              margin-right: 15px;

              &.total {
                color: #409EFF;
              }

              &.completed {
                color: #67C23A;
              }

              &.pending {
                color: #dd524d;

                &.zero-count {
                  color: #999999;
                }
              }

              &.incomplete {
                color: #F56C6C;

                &.zero-count {
                  color: #999999;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
